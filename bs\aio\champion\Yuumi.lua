	-- 自定义自动加点逻辑 - 已移除内置模块

	-- 添加 GetCanLevel 函数 - 检查是否有技能点可用
	local function GetCanLevel()
		if not player or not player.levelRef then
			return false
		end

		-- 计算已使用的技能点
		local usedPoints = 0
		for i = 0, 3 do
			local spell = player:spellSlot(i)
			if spell and spell.level then
				usedPoints = usedPoints + spell.level
			end
		end

		-- 可用技能点 = 当前等级 - 已使用技能点
		local availablePoints = player.levelRef - usedPoints

		-- 调试信息 - 显示每个技能的等级
		local qLevel = player:spellSlot(0).level or 0
		local wLevel = player:spellSlot(1).level or 0
		local eLevel = player:spellSlot(2).level or 0
		local rLevel = player:spellSlot(3).level or 0

		chat.print("Skill Levels: Q=" .. qLevel .. " W=" .. wLevel .. " E=" .. eLevel .. " R=" .. rLevel)
		chat.print("GetCanLevel: Level=" .. player.levelRef .. ", UsedPoints=" .. usedPoints .. ", Available=" .. availablePoints)

		return availablePoints > 0
	end

	local slot_15_48 = {
		r_onspell_time = 0,
		pause_q = 0,
		is_respawned = false,
		last_death_time = 0,
		was_recalling = false,
		last_shop_check = 0,  -- 添加商店检查计时器
		last_ward_time = 0,    -- 插眼计时器
		last_level_check = 0,  -- 自动加点检查计时器
		camera_locked = false, -- 视角锁定状态
		last_scan_time = 0,    -- 扫描计时器
		camera_locked = false, -- 视角锁定状态
		warded_positions = {} -- 已插眼位置记录
	}
	local slot_15_49 = data.Yuumi
	local slot_15_50 = slot_15_49.Q
	local slot_15_51 = slot_15_49.W
	local slot_15_52 = slot_15_49.E
	local slot_15_53 = slot_15_49.R
	local slot_15_54 = 0
	local slot_15_55 = 0
	local slot_15_56 = 0
	local slot_15_57 = 0
	local slot_15_58 = player.charName
	local slot_15_59 = "Yuumi"
	local slot_15_60 = vec2(500, 500)
	local slot_15_61 = vec2(300, 500)
	local slot_15_62 = "sa_Yuumi.json"

	-- 添加AD英雄优先级表
	local adCarries = {
		["Aphelios"] = true,
		["Ashe"] = true,
		["Caitlyn"] = true,
		["Corki"] = true,
		["Draven"] = true,
		["Ezreal"] = true,
		["Jhin"] = true,
		["Jinx"] = true,
		["Kaisa"] = true,
		["Kalista"] = true,
		["Kindred"] = true,
		["KogMaw"] = true,
		["Lucian"] = true,
		["MissFortune"] = true,
		["Samira"] = true,
		["Senna"] = true,
		["Sivir"] = true,
		["Tristana"] = true,
		["Twitch"] = true,
		["Varus"] = true,
		["Vayne"] = true,
		["Xayah"] = true,
		["Ziggs"] = true,
		["Veigar"] = true,
		["Zeri"] = true,
		["Nilah"] = true,
	}

	function slot_15_48.load()
		slot_15_48.menu = menu("BS_Yuumi ", "[Brian]_Yuumi ")

		slot_15_48.menu:menu("q", CN and "Q 技能设置" or "Q Settings")
		slot_15_48.menu.q:boolean("combo1", CN and "Q1 自动使用" or "Q1 Auto Use", true)
		slot_15_48.menu.q:boolean("combo", CN and "Q2 自动使用" or "Q Auto Use", true)
		slot_15_48.menu.q:boolean("nograss", CN and "附身AD在草内时不用Q" or "Don't Use Q When AD in Grass", true)
		slot_15_48.menu:menu("w", CN and "W 技能设置" or "W Settings")
		slot_15_48.menu.w:boolean("auto", CN and "W 自动上车逻辑" or "W Auto Logic", true)
		slot_15_48.menu.w:boolean("autoAA", CN and "W 自动AA" or "W Auto AA", false)
		slot_15_48.menu.w:boolean("autoFlee", CN and "W友军死亡自动换车" or "W Auto change vehicles when friendly troops die", true)
		slot_15_48.menu.w:boolean("allyaa", CN and "队友攻击敌人自动附身" or "When a teammate attacks an enemy, he Auto leans over.", true)
		slot_15_48.menu.w:slider("allyhp", CN and "队友最低血量%" or "Ally Hp%", 60, 1, 101, 1)
		
		-- 添加自动寻找AD附身的开关
		slot_15_48.menu.w:header("adpreset", CN and "AD优先设置" or "AD Priority Settings")
		slot_15_48.menu.w:boolean("autoad", CN and "自动寻找AD英雄上车" or "Auto Find AD to Attach", true)
		slot_15_48.menu.w:boolean("afterrespawn", CN and "复活后强制寻找AD" or "Force Find AD After Respawn", true)
		slot_15_48.menu.w:boolean("autodetach", CN and "回城时自动解除附身" or "Auto Detach When Recall", true)
		slot_15_48.menu.w:keybind("findad", CN and "寻找AD英雄上车(按键)" or "Find AD to Attach (Key)", nil, "T")
		slot_15_48.menu.w:keybind("afterrecall", CN and "回城后寻找AD(按键开关)" or "Find AD After Recall (Toggle)", nil, "Y")
		slot_15_48.menu.w:keybind("autofind", CN and "5000码内自动寻找队友附身(开关)" or "Auto Find Allies Within 5000 Range (Toggle)", nil, "H")
		slot_15_48.menu.w:keybind("stopfollow", CN and "停止跟随(开关)" or "Stop Follow (Toggle)", nil, "J")
		
		-- 添加自动购买装备的选项
		slot_15_48.menu:menu("shop", CN and "商店设置" or "Shop Settings")
		slot_15_48.menu.shop:boolean("autobuy", CN and "自动购买装备" or "Auto Buy Items", true)
		slot_15_48.menu.shop:boolean("printitem", CN and "打印物品ID" or "Print Item ID", false)

		-- 添加新功能菜单
		slot_15_48.menu:menu("misc", CN and "其他功能" or "Miscellaneous")
		slot_15_48.menu.misc:boolean("autolevel", CN and "自动加点" or "Auto Level Up", true)
		slot_15_48.menu.misc:slider("levelstart", CN and "开始加点等级" or "Start Level", 1, 1, 18, 1)
		slot_15_48.menu.misc:slider("levelpattern", CN and "加点方案 (1:主Q副E 2:主Q副W 3:主W副E)" or "Level Pattern (1:Q>E>W 2:Q>W>E 3:W>E>Q)", 1, 1, 3, 1)
		slot_15_48.menu.misc:keybind("resetskills", CN and "重置技能点" or "Reset Skills", "T", nil)
		slot_15_48.menu.misc:boolean("autoward", CN and "自动插眼" or "Auto Ward", true)
		slot_15_48.menu.misc:slider("wardrange", CN and "插眼范围" or "Ward Range", 600, 300, 900, 50)
		slot_15_48.menu.misc:boolean("lockcamera", CN and "锁定视角" or "Lock Camera", false)
		slot_15_48.menu.misc:boolean("autoscan", CN and "自动扫描" or "Auto Scan", true)
		slot_15_48.menu.misc:boolean("autoswapscan", CN and "自动换扫描" or "Auto Swap Scan", true)
		slot_15_48.menu.misc:slider("swaplevel", CN and "换扫描等级" or "Swap Scan Level", 9, 6, 12, 1)
		
		slot_15_48.menu:menu("e", CN and "E 技能设置" or "E Settings")
		slot_15_48.menu.e:boolean("enable", CN and "E治疗队友" or "E Healing", true)

		-- 添加新的E技能设置选项
		slot_15_48.menu.e:boolean("autoescape", CN and "自动逃跑[必须开启启动]" or "Auto Escape [Must Enable to Start]", true)
		slot_15_48.menu.e:boolean("useforally", CN and "附体时为友军使用技能" or "Use Skill for Ally When Attached", true)
		slot_15_48.menu.e:slider("allyhpthreshold", CN and "友军血量低于设定值[%]" or "Ally HP Below Threshold [%]", 80, 1, 100, 1)
		slot_15_48.menu.e:boolean("ignoredistance", CN and "开启时忽略友军距离" or "Ignore Ally Distance When Enabled", true)
		slot_15_48.menu.e:boolean("attackspeed", CN and "增加攻速使用" or "Use for Attack Speed Boost", true)
		slot_15_48.menu.e:boolean("chaseescape", CN and "追击/逃跑使用" or "Use for Chase/Escape", true)
		slot_15_48.menu.e:boolean("killally", CN and "如果下个技能能够杀死友军使用" or "Use if Next Skill Can Kill Ally", true)

		slot_15_48.menu.e:header("something", CN and "E治疗列表" or "E Healing List")

		local slot_23_0 = common.GetAllyHeroes()

		for iter_23_0, iter_23_1 in ipairs(slot_23_0) do
			local slot_23_1 = iter_23_1.charName

			if slot_23_1 ~= player.charName then
				slot_15_48.menu.e:menu(slot_23_1, slot_23_1)
				slot_15_48.menu.e[slot_23_1]:slider("hp", CN and "血量%" or "Hp%", 70, 1, 100, 1)
			else
				slot_15_48.menu.e:menu(slot_23_1, slot_23_1)
				slot_15_48.menu.e[slot_23_1]:slider("hp", CN and "血量%" or "Hp%", 60, 1, 100, 1)
			end
		end

		slot_15_48.menu:menu("r", CN and "R 技能设置" or "R Settings")
		slot_15_48.menu.r:boolean("combo", CN and "R 自动使用" or "R Auto Use", true)
		slot_15_48.menu.r:slider("authrhit", CN and "自动R的数量" or "Number of automatic R", 3, 1, 5, 1)
		slot_15_48.menu.r:boolean("combo2", CN and "自动R2" or "Auto R2", true)

		-- 添加友军治疗相关设置
		slot_15_48.menu.r:header("healing", CN and "友军治疗设置" or "Ally Healing Settings")
		slot_15_48.menu.r:boolean("autoheal", CN and "自动R治疗友军" or "Auto R Heal Allies", true)
		slot_15_48.menu.r:boolean("combatonly", CN and "仅战斗中治疗" or "Heal Only in Combat", true)
		slot_15_48.menu.r:slider("healhp", CN and "友军血量阈值%" or "Ally HP Threshold %", 55, 20, 80, 5)
		slot_15_48.menu.r:boolean("priorityad", CN and "优先治疗AD" or "Priority Heal AD", true)
		slot_15_48.menu.r:boolean("emergencyheal", CN and "紧急治疗模式" or "Emergency Heal Mode", true)
		slot_15_48.menu.r:slider("emergencyhp", CN and "紧急治疗阈值%" or "Emergency HP Threshold %", 25, 10, 40, 5)
		slot_15_48.menu:menu("keys", CN and "按键设置" or "Keys Settings")
		slot_15_48.menu.keys:keybind("combokey", CN and "连招" or "Combo", "Space", nil)
		slot_15_48.menu.keys:keybind("harasskey", CN and "骚扰" or "Haras", "C", nil)
		slot_15_48.menu.keys:keybind("clearkey", CN and "清线/清野" or "Farm", "V", nil)
		slot_15_48.menu.keys:keybind("lastkey", CN and "最后一击" or "Last", "X", nil)
		slot_15_48.menu.keys:keybind("fleekey", CN and "逃跑" or "Flee", "Z", nil)
        slot_15_48.menu.keys:keybind('combo1', CN and "自动Q开关" or "Q1 Auto Use", nil, "G")
		if slot_15_58 ~= "Viego" and slot_15_58 ~= "Sylas" then
			box.addMenu(slot_15_48.menu)
			orb.combat.register_f_pre_tick(slot_15_48.on_tick)
			add_spell(slot_15_48.on_spell)
			add_draw_work(slot_15_48.on_draw)
		end
	end

	function slot_15_48.SetMana()
		slot_15_50.slot = player:spellSlot(0)
		slot_15_51.slot = player:spellSlot(1)
		slot_15_52.slot = player:spellSlot(2)
		slot_15_53.slot = player:spellSlot(3)
		slot_15_54 = slot_15_50.slot.level > 0 and player.manaCost0 or 0
		slot_15_55 = slot_15_51.slot.level > 0 and player.manaCost1 or 0
		slot_15_56 = slot_15_52.slot.level > 0 and player.manaCost2 or 0
		slot_15_57 = slot_15_53.slot.level > 0 and player.manaCost3 or 0
	end

	function slot_15_48.trace_filter(arg_25_0, arg_25_1, arg_25_2)
		local slot_25_0 = arg_25_1.startPos:dist(arg_25_1.endPos)
		local slot_25_1 = arg_25_0.range
		local slot_25_2 = arg_25_1.startPos:dist(arg_25_2.path.serverPos)

		if arg_25_1.hit > 6 then
			return true
		end

		if slot_25_1 < slot_25_0 then
			return false
		end

		if slot_25_1 < slot_25_2 then
			return false
		end

		if slot_25_0 < 650 then
			return true
		end

		if preds.trace.newpath(arg_25_2, 0.033, 0.5) then
			return true
		end
	end

	function slot_15_48.Auto_Q_logic()
		-- 检查是否附身在AD身上且AD在草内时不用Q
		if slot_15_48.menu.q.nograss:get() and slot_15_48.IsW and slot_15_48.WTarget then
			-- 检查WTarget是否是AD英雄（通常是远程物理输出）
			local isAD = false
			if slot_15_48.WTarget.charName:find("Jinx") or slot_15_48.WTarget.charName:find("Caitlyn") or
			   slot_15_48.WTarget.charName:find("Ashe") or slot_15_48.WTarget.charName:find("Vayne") or
			   slot_15_48.WTarget.charName:find("Tristana") or slot_15_48.WTarget.charName:find("Ezreal") or
			   slot_15_48.WTarget.charName:find("Lucian") or slot_15_48.WTarget.charName:find("Sivir") or
			   slot_15_48.WTarget.charName:find("Twitch") or slot_15_48.WTarget.charName:find("Draven") or
			   slot_15_48.WTarget.charName:find("Jhin") or slot_15_48.WTarget.charName:find("Xayah") or
			   slot_15_48.WTarget.charName:find("Kaisa") or slot_15_48.WTarget.charName:find("Aphelios") or
			   slot_15_48.WTarget.charName:find("Samira") or slot_15_48.WTarget.charName:find("Varus") or
			   slot_15_48.WTarget.charName:find("MissFortune") or slot_15_48.WTarget.charName:find("Kalista") or
			   slot_15_48.WTarget.charName:find("Kogmaw") or slot_15_48.WTarget.charName:find("Zeri") or
			   slot_15_48.WTarget.charName:find("Nilah") then
				isAD = true
			end

			-- 如果附身在AD身上且AD在草内，则不使用Q技能
			if isAD and navmesh.isGrass(slot_15_48.WTarget.pos) then
				return
			end
		end

		-- Q2控制逻辑（附身状态下）
		if slot_15_48.IsW and slot_15_50.obj and slot_15_48.menu.q.combo:get() then
			for iter_26_0, iter_26_1 in ipairs(common.GetEnemyHeroes()) do
				if common.isTarget(iter_26_1) and iter_26_1.pos:dist(slot_15_50.obj.pos) < 1500 then
					local slot_26_0 = slot_15_48.FindQPosReturnColl(iter_26_1)

					if slot_26_0 then
						slot_15_48.q_pos = slot_26_0
						slot_15_48.qtarget = iter_26_1
						return
					end
				end
			end
		end

		-- Q2位置计算和备选目标
		if slot_15_48.qtarget and slot_15_48.IsW and slot_15_50.obj and slot_15_48.menu.q.combo:get() then
			local slot_26_1, slot_26_2, slot_26_3 = slot_15_48.FindQPos(slot_15_48.qtarget)

			if not slot_26_3 then
				for iter_26_2, iter_26_3 in ipairs(common.GetEnemyHeroes()) do
					if common.isTarget(iter_26_3) and iter_26_3.ptr ~= slot_15_48.qtarget.ptr and iter_26_3.pos:dist(slot_15_50.obj.pos) < 1500 then
						local slot_26_4, slot_26_5, slot_26_6 = slot_15_48.FindQPos(iter_26_3)

						if slot_26_4 and slot_26_6 then
							slot_15_48.q_pos = slot_26_4
							slot_15_48.q_pos2 = slot_26_5
							slot_15_48.qtarget = iter_26_3

							return
						end
					end
				end
			end

			if slot_26_1 then
				slot_15_48.q_pos = slot_26_1
				slot_15_48.q_pos2 = slot_26_2
			end
		end

		-- Q1释放逻辑（附身状态）
		if common.isq() and not slot_15_50.obj and slot_15_48.IsW and slot_15_48.menu.q.combo1:get() and game.time > slot_15_48.pause_q then
			TS.gettarget(1400, 1, function(arg_27_0)
				if arg_27_0 and #common.GetMinionInRange(200, arg_27_0.pos) == 0 then
					local slot_27_0 = preds.linear.get_prediction(slot_15_50.prediction2, arg_27_0)

					if slot_27_0 and slot_27_0:length() < 1400 and slot_15_48.trace_filter(slot_15_50.prediction2, slot_27_0, arg_27_0) then
						local slot_27_1, slot_27_2 = slot_15_48.FindQPos(arg_27_0)

						if slot_27_1 and player:castSpell("pos", 0, slot_27_1) then
							slot_15_48.qtarget = arg_27_0
							slot_15_48.q_pos = slot_27_1
							slot_15_48.q_pos2 = slot_27_2
							slot_15_48.pause_q = game.time + 0.8

							return
						end
					end
				end
			end)
		end

		-- 击杀逻辑
		if slot_15_50.obj and slot_15_48.IsW and slot_15_48.menu.q.combo:get() then
			for iter_26_4, iter_26_5 in ipairs(common.GetEnemyHeroes()) do
				if common.isTarget(iter_26_5) and iter_26_5.pos:dist(slot_15_50.obj.pos) < 1500 then
					local qDamage = slot_15_50.damage(iter_26_5)
					if qDamage >= iter_26_5.health and iter_26_5.health > 0 then
						local slot_26_0 = slot_15_48.FindQPosReturnColl(iter_26_5)
						if slot_26_0 then
							slot_15_48.q_pos = slot_26_0
							slot_15_48.qtarget = iter_26_5
							return
						end
					end
				end
			end
		end

		-- 未附身状态下的Q技能
		if slot_15_48.menu.keys.combokey:get() and common.isq() and not slot_15_48.IsW and slot_15_48.menu.q.combo1:get() then
			local slot_26_7 = TS.gettarget(slot_15_50.prediction.range, 1)

			if slot_26_7 and common.isTarget(slot_26_7) then
				local slot_26_8 = preds.linear.get_prediction(slot_15_50.prediction, slot_26_7)

				if slot_26_8 and slot_26_8:length() < slot_15_50.prediction.range - 100 and not preds.collision.get_prediction(slot_15_50.prediction, slot_26_8, slot_26_7) and slot_15_48.trace_filter(slot_15_50.prediction, slot_26_8, slot_26_7) then
					player:castSpell("pos", 0, slot_26_8.endPos)
					orb.core.set_server_pause()

					return true
				end
			end
		end
	end

	function slot_15_48.Init()
		slot_15_48.IsW = player.buff.yuumiwattach

		if slot_15_48.WTarget and not slot_15_48.WTarget.buff.yuumiwally then
			slot_15_48.WTarget = nil
		end

		if slot_15_48.IsW then
			for iter_28_0, iter_28_1 in ipairs(common.GetAllyHeroes()) do
				if iter_28_1.buff.yuumiwally then
					slot_15_48.WTarget = iter_28_1

					break
				end
			end
		end

		if (slot_15_48.r_onspell_time < os.clock() or not slot_15_48.IsW) and slot_15_53.pos then
			slot_15_53.pos = nil
			slot_15_48.r_pos = nil
		end

		if slot_15_48.qtarget and player:spellSlot(0).cooldown > 0 then
			slot_15_48.qtarget = nil
			slot_15_48.q_pos = nil
		end
	end

	function slot_15_48.Auto_R_logic()
		-- R技能引导期间的目标跟踪
		if slot_15_53.pos and slot_15_48.menu.r.combo2:get() then
			local slot_29_0 = TS.gettarget(2000, 2, nil, 6, nil, slot_15_53.pos)

			if slot_29_0 then
				slot_15_48.r_pos = slot_29_0.pos

				return
			end
		end

		if player.isRecalling then
			return
		end

		-- 友军治疗逻辑 - 性能优化版本
		if slot_15_48.menu.r.autoheal:get() and common.isr() and slot_15_48.r_onspell_time < os.clock() then
			-- 缓存菜单设置以减少重复调用
			local combatOnly = slot_15_48.menu.r.combatonly:get()
			local healThreshold = slot_15_48.menu.r.healhp:get()
			local emergencyThreshold = slot_15_48.menu.r.emergencyhp:get()
			local emergencyMode = slot_15_48.menu.r.emergencyheal:get()
			local priorityAD = slot_15_48.menu.r.priorityad:get()

			-- 战斗状态检查优化
			local inCombat = false
			if combatOnly then
				inCombat = slot_15_48.IsInCombat()
				if not inCombat then
					return
				end
			end

			local bestAlly = nil
			local lowestHp = 100
			local bestAD = nil
			local lowestADHp = 100
			local emergencyAlly = nil
			local lowestEmergencyHp = 100
			local rRange = slot_15_53.prediction.range

			-- 优化：缓存友军列表，减少重复获取
			local allies = common.GetAllyHeroes()
			for i = 1, #allies do
				local ally = allies[i]
				if ally.ptr ~= player.ptr and not ally.isDead then
					-- 优化：先检查距离再计算血量
					local distance = ally.pos:dist(player.pos)
					if distance <= rRange then
						local allyHpPercent = common.GetHp(ally)

						-- 紧急治疗检查（血量极低时立即治疗，无视战斗状态）
						if emergencyMode and allyHpPercent <= emergencyThreshold and allyHpPercent < lowestEmergencyHp then
							lowestEmergencyHp = allyHpPercent
							emergencyAlly = ally
						end

						-- 检查是否是AD英雄
						if priorityAD and adCarries[ally.charName] then
							if allyHpPercent <= healThreshold and allyHpPercent < lowestADHp then
								lowestADHp = allyHpPercent
								bestAD = ally
							end
						end

						-- 记录血量最低的友军
						if allyHpPercent <= healThreshold and allyHpPercent < lowestHp then
							lowestHp = allyHpPercent
							bestAlly = ally
						end
					end
				end
			end

			-- 治疗优先级：紧急治疗 > AD英雄 > 血量最低友军
			local targetAlly = emergencyAlly or (not combatOnly or inCombat) and (priorityAD and bestAD or bestAlly)

			if targetAlly then
				-- 对友军释放R技能治疗
				player:castSpell("pos", 3, targetAlly.pos)
				local healType = emergencyAlly and "紧急治疗" or (bestAD and "AD治疗" or "友军治疗")
				local combatStatus = inCombat and "[战斗中]" or "[非战斗]"
				chat.print("[Yuumi] " .. combatStatus .. " R技能" .. healType .. ": " .. targetAlly.charName .. " (血量: " .. math.floor(common.GetHp(targetAlly)) .. "%)")
				return
			end
		end

		-- R技能释放逻辑
		if slot_15_48.menu.r.combo:get() and common.isr() and slot_15_48.r_onspell_time < os.clock() then
			local slot_29_1
			local slot_29_2 = 0
			local slot_29_3 = common.GetEnemyHeroes()

			for iter_29_0, iter_29_1 in ipairs(slot_29_3) do
				if iter_29_1 and common.isTarget(iter_29_1) and iter_29_1.pos:dist(player.pos) <= slot_15_53.prediction.range then
					local slot_29_4 = player.pos
					local slot_29_5 = 1
					local slot_29_6 = {
						obj = iter_29_1
					}
					local slot_29_7 = iter_29_1.pos:dist(player.pos) / 3000
					local slot_29_8 = preds.core.get_pos_after_time(iter_29_1, slot_29_7)

					slot_29_6.startPos = slot_29_8
					slot_29_6.endPos = slot_29_4 + (slot_29_8 - slot_29_4):norm() * slot_15_53.prediction.range

					-- 计算可以命中的敌人数量
					for iter_29_2, iter_29_3 in ipairs(slot_29_3) do
						if iter_29_3 and iter_29_3.ptr ~= iter_29_1.ptr and common.isTarget(iter_29_3) then
							local slot_29_9 = preds.linear.get_prediction(slot_15_53.prediction, iter_29_3, to2D(slot_29_8))

							if slot_29_9 and slot_29_9.endPos:dist(slot_29_8) <= slot_15_53.prediction.range then
								local slot_29_10, slot_29_11, slot_29_12 = common.VectorPointProjectionOnLineSegment(to2D(slot_29_6.startPos), to2D(slot_29_6.endPos), to2D(slot_29_9.endPos))

								if slot_29_12 and slot_29_10 and vec3(slot_29_10.x, 0, slot_29_10.y):dist(slot_29_9.endPos) <= 240 then
									slot_29_5 = slot_29_5 + 1
								end
							end
						end
					end

					-- 优先级计算：低血量目标优先
					local priority = slot_29_5
					local hpPercent = common.GetHp(iter_29_1)
					if hpPercent <= 50 then
						priority = priority + 0.5
					end

					if slot_29_2 < priority then
						slot_29_2 = priority
						slot_29_1 = slot_29_6
					end
				end
			end

			-- 智能释放条件
			local minHitRequirement = slot_15_48.menu.r.authrhit:get()
			local enemiesNearby = #common.GetEnemyHeroesInRange(1000, player.pos)

			-- 团战中降低要求
			if enemiesNearby >= 3 then
				minHitRequirement = math.max(1, minHitRequirement - 1)
			end

			if slot_29_2 >= minHitRequirement and slot_29_1 then
				player:castSpell("pos", 3, slot_29_1.endPos)
			end
		end
	end

	function slot_15_48.FindWAllyTarget()
		local slot_30_0 = 0
		local slot_30_1
		local adTarget = nil

		for iter_30_0, iter_30_1 in ipairs(common.GetAllyHeroes()) do
			if iter_30_1.ptr ~= player.ptr and not iter_30_1.isDead and iter_30_1.health > 100 then
				-- 检查AD英雄优先级
				if slot_15_48.menu.w.autoad:get() and adCarries[iter_30_1.charName] then
					if not adTarget or adTarget.exp < iter_30_1.exp then
						adTarget = iter_30_1
					end
				end

				if slot_30_0 < iter_30_1.exp then
					slot_30_0 = iter_30_1.exp
					slot_30_1 = iter_30_1
				end
			end
		end

		-- 根据优先级选择附身目标
		return (slot_15_48.menu.w.autoad:get() and adTarget) or slot_30_1
	end

	slot_15_48.set_pause_w = 0

	function slot_15_48.Auto_W_logic()
		if player.isRecalling then
			return
		end

		-- 检查是否开启了停止跟随
		if slot_15_48.menu.w.stopfollow:get() then
			-- 如果当前已附身且开启了停止跟随，则解除附身
			if slot_15_48.IsW and common.isw() then
				player:castSpell("pos", 1, player.pos)
			end
			return
		end

		-- 如果队友已经死亡且开启了自动上车逻辑，则寻找附身目标
		if slot_15_48.IsW and slot_15_48.WTarget and slot_15_48.WTarget.isDead then
			local slot_31_0 = slot_15_48.FindWAllyTarget()

			if slot_31_0 and (not evade or evade.core.is_action_safe(slot_31_0.pos, 1300, 0.25)) then
				if slot_31_0.pos:dist(player.pos) <= slot_15_51.prediction.range then
					player:castSpell("obj", 1, slot_31_0)
					return
				end
			end
		end
		
		-- 如果队友没有附身且没有在回城且可以行动且开启了自动寻找附身，则寻找附身目标
		if not slot_15_48.IsW and not player.isRecalling and orb.core.can_action() and slot_15_48.menu.w.autofind:get() and not slot_15_48.menu.w.stopfollow:get() then
			local bestAlly = nil
			local closestDistance = 5000
			local bestAD = nil
			local closestADDistance = 5000
			
			for _, ally in ipairs(common.GetAllyHeroes()) do
				if ally.ptr ~= player.ptr and not ally.isDead and ally.health > 100 then
					local distance = ally.pos:dist(player.pos)
					
					-- 检查是否已经附身在队友身上
					if slot_15_48.menu.w.autoad:get() and adCarries[ally.charName] then
						if distance < closestADDistance then
							closestADDistance = distance
							bestAD = ally
						end
					end
					
					-- 选择距离最近的队友
					if distance < closestDistance then
						closestDistance = distance
						bestAlly = ally
					end
				end
			end
			
			-- 根据优先级选择附身目标
			local targetAlly = (slot_15_48.menu.w.autoad:get() and bestAD) or bestAlly
			local targetDistance = (slot_15_48.menu.w.autoad:get() and bestAD) and closestADDistance or closestDistance
			
			if targetAlly then
				if targetDistance <= slot_15_51.prediction.range then
					-- 直接附身到队友身上
					player:castSpell("obj", 1, targetAlly)
					common.d_debug("try to attach to " .. targetAlly.charName .. (adCarries[targetAlly.charName] and " (AD)" or ""))
					return
				else
					-- 移动到队友身边附身
					player:move(targetAlly.pos)
					common.d_debug("moving to " .. targetAlly.charName .. (adCarries[targetAlly.charName] and " (AD)" or "") .. " dist: " .. math.floor(targetDistance))
					return
				end
			end
		end

		if slot_15_48.menu.w.autoAA:get() and common.isw() and slot_15_48.IsW and orb.core.can_action() and player.passiveCooldownEndTime - game.time < 0 then
			local slot_31_0 = slot_15_48.FindWAllyTarget()

			if slot_31_0 and (not evade or evade.core.is_action_safe(slot_31_0.pos, 1300, 0.25)) then
				local slot_31_1 = TS.getorbtarget(100)

				if slot_31_1 and not common.ista(slot_31_1.pos, 50) and #common.CountEnemiesInRange(slot_31_1.pos, 750) < 3 and player:castSpell("pos", 1, slot_31_1.pos) then
					player:attack(slot_31_1)
					orb.core.set_server_pause()

					slot_15_48.set_pause_w = os.clock() + 0.4

					return
				end
			end
		end

		if slot_15_48.WTarget and slot_15_48.menu.w.autoFlee:get() and slot_15_48.IsW and slot_15_48.WTarget.health - common.pred_damage(slot_15_48.WTarget, 1, true) <= 0 then
			local slot_31_2 = slot_15_48.FindWAllyTarget()

			if slot_31_2 and (not evade or evade.core.is_action_safe(slot_31_2.pos, 1300, 0.25)) then
				player:castSpell("obj", 1, slot_31_2)
				common.d_debug("w1")

				return
			end
		end

		if slot_15_48.menu.w.auto:get() and common.isw() and not slot_15_48.WTarget and not slot_15_48.IsW and orb.core.can_action() and (slot_15_48.set_pause_w < os.clock() or not orb.core.can_attack()) and game.time > 10 and not slot_15_48.menu.w.stopfollow:get() then
			local slot_31_3 = slot_15_48.FindWAllyTarget()

			if slot_31_3 and (not evade or evade.core.is_action_safe(slot_31_3.pos, 1300, 0)) then
				player:castSpell("obj", 1, slot_31_3)
				common.d_debug("w2")

				return
			end
		end
	end

	function slot_15_48.Auto_E_logic()
		if player.isRecalling then
			return
		end

		-- 检查基本条件
		if not slot_15_48.menu.e.enable:get() or not common.ise() then
			return
		end

		-- 自动逃跑检查 - 必须开启才能启动其他功能
		if not slot_15_48.menu.e.autoescape:get() then
			return
		end

		-- 检查是否已经附身在队友身上
		if slot_15_48.IsW and slot_15_48.WTarget then
			-- 附体时为友军使用技能检查
			if not slot_15_48.menu.e.useforally:get() then
				return
			end

			-- 获取队友当前血量百分比
			local allyHpPercent = common.GetHp(slot_15_48.WTarget)

			-- 使用新的友军血量阈值设置
			local healThreshold = slot_15_48.menu.e.allyhpthreshold:get()
			if slot_15_48.menu.e[slot_15_48.WTarget.charName] then
				healThreshold = slot_15_48.menu.e[slot_15_48.WTarget.charName].hp:get()
			end

			-- 距离检查（除非忽略距离选项开启）
			local distanceCheck = true
			if not slot_15_48.menu.e.ignoredistance:get() then
				-- 检查距离，E技能范围内
				if slot_15_48.WTarget.pos:dist(player.pos) > 1000 then
					distanceCheck = false
				end
			end

			-- 如果下个技能能够杀死友军使用检查
			local canKillAlly = false
			if slot_15_48.menu.e.killally:get() then
				-- 检查是否有敌人即将对友军造成致命伤害
				local predictedDamage = common.pred_damage(slot_15_48.WTarget, 1, true)
				if slot_15_48.WTarget.health - predictedDamage <= 0 then
					canKillAlly = true
				end
			end

			-- 追击/逃跑使用检查
			local chaseEscapeCondition = false
			if slot_15_48.menu.e.chaseescape:get() then
				-- 检查是否在追击或逃跑状态
				if slot_15_48.WTarget.path.isActive and slot_15_48.WTarget.moveSpeed > 350 then
					chaseEscapeCondition = true
				end
			end

			-- 增加攻速使用检查
			local attackSpeedCondition = false
			if slot_15_48.menu.e.attackspeed:get() then
				-- 检查是否需要攻速加成（比如在攻击敌人时）
				if orb.combat.target and orb.combat.target.team ~= player.team then
					attackSpeedCondition = true
				end
			end

			-- 决定是否治疗
			local shouldHeal = false

			-- 紧急治疗：血量极低时立即治疗
			if allyHpPercent <= 25 then
				shouldHeal = true
			-- 常规治疗：血量低于阈值且护盾较少时治疗
			elseif allyHpPercent < healThreshold and slot_15_48.WTarget.allShield <= 50 then
				shouldHeal = true
			-- 如果下个技能能杀死友军，立即治疗
			elseif canKillAlly then
				shouldHeal = true
			-- 追击/逃跑时的治疗
			elseif chaseEscapeCondition and allyHpPercent < healThreshold + 10 then
				shouldHeal = true
			-- 攻速加成治疗
			elseif attackSpeedCondition and allyHpPercent < healThreshold + 15 then
				shouldHeal = true
			end

			if shouldHeal and distanceCheck then
				player:castSpell("self", 2)
				common.d_debug("Healing " .. slot_15_48.WTarget.charName .. ", HP: " .. math.floor(allyHpPercent) .. "%, Threshold: " .. healThreshold .. "%")
				return
			end
		end

		-- 自己血量低时也治疗自己
		if not slot_15_48.IsW then
			local selfHpPercent = common.GetHp(player)
			-- 自己的默认治疗阈值60%
			local selfHealThreshold = 60
			if slot_15_48.menu.e[player.charName] then
				selfHealThreshold = slot_15_48.menu.e[player.charName].hp:get()
			end

			-- 只有血量真正低于阈值时才治疗自己
			if selfHpPercent < selfHealThreshold then
				player:castSpell("self", 2)
				common.d_debug("Healing self, HP: " .. math.floor(selfHpPercent) .. "%, Threshold: " .. selfHealThreshold .. "%")
				return
			end
		end
	end

	-- 娣诲姞涓€涓?鐗规畩鍑芥暟鐢ㄤ簬璋冭瘯E鎶€鑳?
	function slot_15_48.Debug_E_Status()
		if slot_15_48.IsW and slot_15_48.WTarget then
			common.d_debug("Attached to: " .. slot_15_48.WTarget.charName)
			common.d_debug("HP: " .. math.floor(common.GetHp(slot_15_48.WTarget)) .. "%")
			
			if slot_15_48.menu.e[slot_15_48.WTarget.charName] then
				common.d_debug("Threshold: " .. slot_15_48.menu.e[slot_15_48.WTarget.charName].hp:get() .. "%")
			else
				common.d_debug("No threshold set")
			end
			
			common.d_debug("E Ready: " .. (common.ise() and "Yes" or "No"))
			common.d_debug("allShield: " .. slot_15_48.WTarget.allShield)
		else
			common.d_debug("Not attached")
		end
	end

	function slot_15_48.AutoGameLogic()
		if game.time < 900 and common.isw() and not slot_15_48.WTarget and not slot_15_48.IsW and orb.core.can_action() then
			-- block empty
		end
	end

	function slot_15_48.on_tick()
		--if slot_15_48.menu.keys.clearkey:get() then 
		if not slot_15_48.menu.keys.combo1:get() then return end
		--combo1()
		--end
		if common.action_lock.is_locked() then
			orb.SetNextMove(false)
			orb.SetNextAttack(false)

			return
		end

		slot_15_48.Init()
		
		-- 执行自动购买装备功能
		slot_15_48.AutoBuyItems()
		
		-- 回城状态监测
		local is_recalling_now = player.isRecalling
		
		-- 检测回城完成时刻，使用正确方法检测是否在泉水区域
		if slot_15_48.was_recalling and not is_recalling_now and not player.isDead and player.inShopRange then
			-- 回城完成，自动寻找AD
			if slot_15_48.menu.w.afterrecall:get() then
				local bestAD = slot_15_48.FindAdTarget()
				
				if bestAD then
					if bestAD.pos:dist(player.pos) <= slot_15_51.prediction.range then
						player:castSpell("obj", 1, bestAD)
						common.d_debug("After recall: attaching to AD: " .. bestAD.charName)
						return
					else
						player:move(bestAD.pos)
						common.d_debug("After recall: moving to AD: " .. bestAD.charName)
						return
					end
				end
			end
		end
		
		-- 更新回城状态
		slot_15_48.was_recalling = is_recalling_now
		
		-- 添加回城时自动解除附身功能
		if slot_15_48.IsW and slot_15_48.menu.w.autodetach:get() and player.isRecalling then
			if common.isw() then
				player:castSpell("pos", 1, player.pos)
				common.d_debug("Auto detach while recalling")
				return
			end
		end
		
		-- 添加快捷键寻找AD功能
		if slot_15_48.menu.w.findad:get() and not player.isRecalling and not slot_15_48.IsW then
			local bestAD = slot_15_48.FindAdTarget()
			
			if bestAD then
				if bestAD.pos:dist(player.pos) <= slot_15_51.prediction.range then
					player:castSpell("obj", 1, bestAD)
					common.d_debug("(Key) Attaching to AD: " .. bestAD.charName)
					return
				else
					player:move(bestAD.pos)
					common.d_debug("(Key) Moving to AD: " .. bestAD.charName)
					return
				end
			end
		end
		
		-- 检查玩家是否死亡，设置死亡时间
		if player.isDead then
			slot_15_48.last_death_time = game.time
			slot_15_48.is_respawned = true
		end
		
		-- 检查是否刚复活且两个功能开关均已开启
		if not player.isDead and not slot_15_48.IsW and (game.time - slot_15_48.last_death_time < 10) and slot_15_48.is_respawned and slot_15_48.menu.w.afterrespawn:get() and slot_15_48.menu.w.autoad:get() then
			-- 寻找地图上任何位置的AD去附身
			local bestAD = slot_15_48.FindAdTarget()
			
			if bestAD then
				if bestAD.pos:dist(player.pos) <= slot_15_51.prediction.range then
					player:castSpell("obj", 1, bestAD)
					slot_15_48.is_respawned = false
					common.d_debug("Found AD in range after respawn")
					return
				else
					player:move(bestAD.pos)
					common.d_debug("Moving to AD after respawn: " .. bestAD.charName)
					return
				end
			end
		end
		
		-- 如果玩家已附身，取消复活标记
		if slot_15_48.is_respawned and slot_15_48.IsW then
			slot_15_48.is_respawned = false
		end
		
		-- 性能优化：使用时间间隔控制不同功能的调用频率
		local currentTime = os.clock()

		-- 核心技能逻辑 - 每帧执行（最高优先级）
		slot_15_48.Auto_Q_logic()
		slot_15_48.Auto_W_logic()
		slot_15_48.Auto_E_logic()
		slot_15_48.Auto_R_logic()

		-- 游戏逻辑 - 每0.1秒执行一次
		if not slot_15_48.last_game_logic_time or currentTime - slot_15_48.last_game_logic_time >= 0.1 then
			slot_15_48.AutoGameLogic()
			slot_15_48.last_game_logic_time = currentTime
		end

		-- 低频功能 - 每0.5秒执行一次
		if not slot_15_48.last_low_freq_time or currentTime - slot_15_48.last_low_freq_time >= 0.5 then
			slot_15_48.AutoLevelUp()
			slot_15_48.LockCamera()
			slot_15_48.last_low_freq_time = currentTime
		end

		-- 超低频功能 - 每1秒执行一次
		if not slot_15_48.last_ultra_low_freq_time or currentTime - slot_15_48.last_ultra_low_freq_time >= 1.0 then
			slot_15_48.AutoWard()
			slot_15_48.AutoScan()
			slot_15_48.AutoSwapScan()
			slot_15_48.last_ultra_low_freq_time = currentTime
		end
	end

	function slot_15_48.on_spell(arg_35_0, arg_35_1)
		local slot_35_0 = arg_35_0
		local slot_35_1 = arg_35_1.target
		local slot_35_2 = arg_35_1.name
		local slot_35_3 = arg_35_1.windUpTime

		if slot_35_0 and slot_35_0.ptr == player.ptr and slot_35_2 == "YuumiR" then
			slot_15_48.r_onspell_time = os.clock() + 3.5
		end

		if arg_35_1 and slot_35_0 and slot_15_48.WTarget and common.ise() and slot_15_48.WTarget.allShield < 50 and slot_35_0.team ~= player.team and slot_35_1 and slot_35_1.type == TYPE_HERO and slot_35_1.team == player.team and slot_35_1.ptr == slot_15_48.WTarget.ptr and common.ise() and slot_15_48.menu.e.enable:get() and slot_35_1.pos:dist(player.pos) <= slot_15_52.prediction.range and slot_35_2:find("Turret") and slot_15_48.menu.e[slot_15_48.WTarget.charName].hp:get() >= common.GetHp(slot_15_48.WTarget) then
			player:castSpell("self", 2)
		end
	end

	function slot_15_48.FindQPosReturnColl(arg_36_0)
		if not arg_36_0 then
			return
		end

		local slot_36_0 = slot_15_50.obj and slot_15_50.obj.pos or player.pos
		local slot_36_1 = preds.linear.get_prediction(slot_15_50.prediction2, arg_36_0, slot_36_0)
		local slot_36_2 = slot_36_1.endPos
		local slot_36_3 = slot_15_50.obj and (1 - (os.clock() - slot_15_50.CastTime)) * 950 or 850
		local slot_36_4 = slot_36_1

		if slot_36_3 >= slot_36_1:length() and not preds.collision.get_prediction(slot_15_50.prediction2, slot_36_4, arg_36_0) then
			if slot_15_50.obj then
				common.d_debug(" > not coll2")
			end

			return slot_36_2
		end
	end

	function slot_15_48.FindQPos(arg_37_0, arg_37_1)
		if not arg_37_0 then
			return
		end

		local slot_37_0 = slot_15_50.obj and slot_15_50.obj.pos or player.pos
		local slot_37_1 = preds.linear.get_prediction(slot_15_50.prediction2, arg_37_0, slot_37_0)
		local slot_37_2 = slot_37_1.endPos
		local slot_37_3 = slot_37_1

		if not preds.collision.get_prediction(slot_15_50.prediction2, slot_37_3, arg_37_0) then
			if slot_15_50.obj then
				common.d_debug(" > not coll")
			end

			return slot_37_2, nil, true
		end

		local slot_37_4 = 9999
		local slot_37_5
		local slot_37_6 = common.CirclePoints(30, slot_37_0:dist(slot_37_2) / 2, slot_37_2)

		for iter_37_0, iter_37_1 in ipairs(slot_37_6) do
			if (slot_15_50.obj and (1 - (os.clock() - slot_15_50.CastTime)) * 850 or 850) > iter_37_1:dist(slot_37_0) and slot_37_2:dist(slot_37_0) > iter_37_1:dist(slot_37_0) then
				slot_37_3.endPos = iter_37_1

				if not preds.collision.get_prediction(slot_15_50.prediction2, slot_37_3, arg_37_0) then
					local slot_37_7 = {
						startPos = iter_37_1,
						endPos = slot_37_2
					}

					if not preds.collision.get_prediction(slot_15_50.prediction2, slot_37_7, arg_37_0) then
						local slot_37_8 = iter_37_1:dist(slot_37_0)

						if slot_37_8 < slot_37_4 then
							slot_37_4 = slot_37_8
							slot_37_5 = iter_37_1

							if arg_37_1 then
								DrawCircle3d(iter_37_1, 50, 4294902015, 100, 3)
							end
						end
					end
				end
			end
		end

		if slot_15_50.CastTime and slot_15_50.obj and os.clock() - slot_15_50.CastTime > 0.95 then
			common.d_debug(" > 95")

			return slot_37_1.endPos
		end

		return slot_37_5, slot_37_2
	end

	cb.add(cb.hook_cursorpos, function(arg_38_0)
		if slot_15_48.r_pos then
			local slot_38_0 = graphics.world_to_screen(slot_15_48.r_pos)

			arg_38_0[0] = slot_38_0.x
			arg_38_0[1] = slot_38_0.y
		end

		if not slot_15_50.obj then
			return
		end

		if slot_15_48.q_pos and slot_15_48.IsW then
			local slot_38_1 = graphics.world_to_screen(slot_15_48.q_pos)

			arg_38_0[0] = slot_38_1.x
			arg_38_0[1] = slot_38_1.y
		end
	end)

	function slot_15_48.on_draw()
		if slot_15_50.obj then
			local slot_39_0 = slot_15_50.obj and slot_15_50.obj.pos or player.pos

			if slot_15_48.q_pos and not slot_15_48.q_pos2 then
				DrawLine3d(slot_39_0, slot_15_48.q_pos, 4294902015, 1)
			elseif slot_15_48.q_pos and slot_15_48.q_pos2 then
				DrawLine3d(slot_39_0, slot_15_48.q_pos, 4294902015, 1)
				DrawLine3d(slot_15_48.q_pos, slot_15_48.q_pos2, 4294902015, 1)
			end
		end
	end

	function slot_15_48.on_after_attack()
		return
	end

	function slot_15_48.on_create_missile(arg_41_0)
		if not arg_41_0 or not arg_41_0.spell then
			return
		end

		local slot_41_0 = arg_41_0.spell.owner
		local slot_41_1 = arg_41_0.name

		if slot_41_1 == "YuumiQCast" and slot_41_0 and slot_41_0.ptr == player.ptr then
			slot_15_50.obj = arg_41_0
			slot_15_50.CastTime = os.clock()
		end

		if slot_41_1 == "YuumiRMissile" and slot_41_0 and slot_41_0.ptr == player.ptr then
			slot_15_53.pos = arg_41_0.pos
		end
	end

	function slot_15_48.on_delete_missile(arg_42_0)
		if slot_15_50.obj and arg_42_0 == slot_15_50.obj.ptr then
			slot_15_50.obj = nil
		end
	end

	function slot_15_48.FindAdTarget()
		-- 寻找地图上任何位置的AD
		local bestAD = nil

		-- 寻找最佳AD英雄
		for _, ally in ipairs(common.GetAllyHeroes()) do
			if ally.ptr ~= player.ptr and not ally.isDead and ally.health > 100 then
				if adCarries[ally.charName] then
					if not bestAD or bestAD.exp < ally.exp then
						bestAD = ally
					end
				end
			end
		end

		return bestAD
	end

	-- 检测是否在战斗状态 - 性能优化版本
	function slot_15_48.IsInCombat()
		-- 缓存战斗状态检查结果，减少重复计算
		local currentTime = os.clock()
		if slot_15_48.combat_cache_time and currentTime - slot_15_48.combat_cache_time < 0.2 then
			return slot_15_48.combat_cache_result
		end

		-- 检查玩家是否在战斗中
		if player.inCombat then
			slot_15_48.combat_cache_result = true
			slot_15_48.combat_cache_time = currentTime
			return true
		end

		-- 检查附身的友军是否在战斗中
		if slot_15_48.IsW and slot_15_48.WTarget and slot_15_48.WTarget.inCombat then
			slot_15_48.combat_cache_result = true
			slot_15_48.combat_cache_time = currentTime
			return true
		end

		local playerPos = player.pos

		-- 优化：检查附近是否有敌人（1200范围内）
		local enemies = common.GetEnemyHeroes()
		for i = 1, #enemies do
			local enemy = enemies[i]
			if not enemy.isDead then
				local distSq = (enemy.pos.x - playerPos.x)^2 + (enemy.pos.y - playerPos.y)^2
				if distSq <= 1440000 then -- 1200^2 = 1440000
					slot_15_48.combat_cache_result = true
					slot_15_48.combat_cache_time = currentTime
					return true
				end
			end
		end

		-- 优化：检查友军是否在战斗中（800范围内的友军）
		local allies = common.GetAllyHeroes()
		for i = 1, #allies do
			local ally = allies[i]
			if ally.ptr ~= player.ptr and not ally.isDead and ally.inCombat then
				local distSq = (ally.pos.x - playerPos.x)^2 + (ally.pos.y - playerPos.y)^2
				if distSq <= 640000 then -- 800^2 = 640000
					slot_15_48.combat_cache_result = true
					slot_15_48.combat_cache_time = currentTime
					return true
				end
			end
		end

		slot_15_48.combat_cache_result = false
		slot_15_48.combat_cache_time = currentTime
		return false
	end

	-- 将 AutoLevelUp 直接实现为您提供的原始逻辑
	function slot_15_48.AutoLevelUp()
		-- 检查菜单是否已初始化
		if not slot_15_48.menu or not slot_15_48.menu.misc then
			return
		end

		-- 安全获取菜单值，避免nil比较
		local autolevelEnabled = slot_15_48.menu.misc.autolevel and slot_15_48.menu.misc.autolevel:get() or false
		local levelStart = slot_15_48.menu.misc.levelstart and slot_15_48.menu.misc.levelstart:get() or 1
		local levelPattern = slot_15_48.menu.misc.levelpattern and slot_15_48.menu.misc.levelpattern:get() or 1

		-- 安全检查 player 和 levelRef
		if not player or not player.levelRef then
			return
		end



		if autolevelEnabled and player.levelRef >= levelStart then
			-- 定义加点方案
			local skillOrder = {}

			if levelPattern == 1 then -- Q>W>E, R at 6,11,16 (Q:6级, W:5级, E:5级, R:3级)
				skillOrder = {0, 2, 1, 0, 0, 3, 0, 1, 0, 1, 3, 1, 2, 2, 0, 3, 1, 2}
			elseif levelPattern == 2 then -- Q>E>W, R at 6,11,16 (Q:6级, E:5级, W:5级, R:3级)
				skillOrder = {0, 2, 1, 0, 0, 3, 0, 2, 0, 2, 3, 2, 2, 1, 1, 3, 1, 0}
			elseif levelPattern == 3 then -- W>E>Q, R at 6,11,16 (W:5级, E:5级, Q:6级, R:3级)
				skillOrder = {2, 0, 1, 0, 1, 3, 1, 2, 2, 1, 3, 2, 2, 0, 0, 3, 0, 0}
			end

			if skillOrder[player.levelRef] then
				local slot = skillOrder[player.levelRef]
				-- local skillName = (slot == 0 and "Q") or (slot == 1 and "W") or (slot == 2 and "E") or "R"

				-- 检查技能是否已经满级
				local currentLevel = player:spellSlot(slot).level or 0
				local maxLevel

				-- 根据加点方案设置不同的最大等级
				if levelPattern == 1 then -- Q>W>E
					if slot == 0 then maxLevel = 6      -- Q: 6级
					elseif slot == 1 then maxLevel = 5  -- W: 5级
					elseif slot == 2 then maxLevel = 5  -- E: 5级
					else maxLevel = 3 end               -- R: 3级
				elseif levelPattern == 2 then -- Q>E>W
					if slot == 0 then maxLevel = 6      -- Q: 6级
					elseif slot == 1 then maxLevel = 5  -- W: 5级
					elseif slot == 2 then maxLevel = 5  -- E: 5级
					else maxLevel = 3 end               -- R: 3级
				else -- levelPattern == 3, W>E>Q
					if slot == 0 then maxLevel = 6      -- Q: 6级
					elseif slot == 1 then maxLevel = 5  -- W: 5级
					elseif slot == 2 then maxLevel = 5  -- E: 5级
					else maxLevel = 3 end               -- R: 3级
				end

				if currentLevel < maxLevel then
					player:levelSpell(slot)
					-- chat.print("AutoLevel: Leveled " .. skillName .. " at Level " .. player.levelRef)
				else
					-- chat.print("AutoLevel: " .. skillName .. " already at max level (" .. currentLevel .. "/" .. maxLevel .. ")")
				end
			elseif player.levelRef <= 18 then
				-- chat.print("AutoLevel: No skill to level at Level " .. player.levelRef)
			end
		end
	end



	-- 自动插眼功能 (距离草丛500码就插眼)
	function slot_15_48.AutoWard()
		if not slot_15_48.menu or not slot_15_48.menu.misc then
			return
		end

		if not slot_15_48.menu.misc.autoward:get() then
			return
		end

		-- 限制插眼检查频率，每1秒检查一次
		if os.clock() - slot_15_48.last_ward_time < 1 then
			return
		end

		if not player then
			return
		end

		-- 检查是否有眼石或饰品眼
		local wardSlot = nil
		local wardNames = {
			["ItemGhostWard"] = true,
			["TrinketTotemLvl1"] = true,
			["TrinketTotemLvl3"] = true,
			["TrinketTotemLvl4"] = true,
			["VisionWard"] = true,
			["JammerDevice"] = true
		}

		for slot = 6, 12 do
			local spellSlot = player:spellSlot(slot)
			if spellSlot and spellSlot.isNotEmpty and spellSlot.state == 0 and wardNames[spellSlot.name] then
				wardSlot = slot
				break
			end
		end

		if wardSlot then
			local spellSlot = player:spellSlot(wardSlot)
			if spellSlot and spellSlot.cooldown <= 0 then
				-- 寻找500码范围内的草丛
				local nearestGrass = nil
				local nearestDistance = 999999

				-- 搜索500码范围内的草丛
				for angle = 0, 315, 15 do  -- 更密集的搜索
					local rad = math.rad(angle)
					for distance = 50, 500, 25 do  -- 搜索到500码
						local testPos = player.pos + vec3(math.cos(rad) * distance, 0, math.sin(rad) * distance)
						if navmesh.isGrass(testPos) then
							local distToPlayer = player.pos:dist(testPos)
							if distToPlayer < nearestDistance then
								nearestDistance = distToPlayer
								nearestGrass = testPos
							end
						end
					end
				end

				-- 如果找到了500码内的草丛
				if nearestGrass then
					-- 寻找草丛中心位置作为插眼点
					local wardPos = nearestGrass
					local bestPos = nearestGrass
					local maxGrassCount = 0

					-- 在找到的草丛周围寻找最佳插眼位置（草丛中心）
					for angle = 0, 315, 45 do
						local rad = math.rad(angle)
						for distance = 0, 150, 50 do
							local testPos = nearestGrass + vec3(math.cos(rad) * distance, 0, math.sin(rad) * distance)
							if navmesh.isGrass(testPos) then
								-- 计算这个位置周围的草丛密度
								local grassCount = 0
								for checkAngle = 0, 315, 45 do
									local checkRad = math.rad(checkAngle)
									local checkPos = testPos + vec3(math.cos(checkRad) * 50, 0, math.sin(checkRad) * 50)
									if navmesh.isGrass(checkPos) then
										grassCount = grassCount + 1
									end
								end

								if grassCount > maxGrassCount then
									maxGrassCount = grassCount
									bestPos = testPos
								end
							end
						end
					end

					wardPos = bestPos

					-- 检查这个位置是否已经插过眼（400单位内）
					local alreadyWarded = false
					for _, wardedPos in ipairs(slot_15_48.warded_positions) do
						if wardPos:dist(wardedPos.pos) < 400 and (os.clock() - wardedPos.time) < 180 then
							alreadyWarded = true
							break
						end
					end

					if not alreadyWarded then
						player:castSpell("pos", wardSlot, wardPos)

						-- 记录插眼位置和时间
						table.insert(slot_15_48.warded_positions, {
							pos = vec3(wardPos.x, wardPos.y, wardPos.z),
							time = os.clock()
						})

						-- 清理过期的插眼记录（超过3分钟）
						for i = #slot_15_48.warded_positions, 1, -1 do
							if os.clock() - slot_15_48.warded_positions[i].time > 180 then
								table.remove(slot_15_48.warded_positions, i)
							end
						end
					end
				end
			end
		end

		-- 更新检查时间戳
		slot_15_48.last_ward_time = os.clock()
	end

	-- 锁定视角功能 (使用game.setCameraLock API)
	function slot_15_48.LockCamera()
		-- 检查菜单设置
		if not slot_15_48.menu or not slot_15_48.menu.misc then
			return
		end

		local shouldLock = slot_15_48.menu.misc.lockcamera:get()

		-- 如果设置改变了，更新状态
		if shouldLock and not slot_15_48.camera_locked then
			-- 需要锁定视角但当前未锁定
			if game.setCameraLock then
				game.setCameraLock(true)
				slot_15_48.camera_locked = true
				chat.print("[Yuumi] 自动锁定视角")
			end
		elseif not shouldLock and slot_15_48.camera_locked then
			-- 需要解锁视角但当前已锁定
			if game.setCameraLock then
				game.setCameraLock(false)
				slot_15_48.camera_locked = false
				chat.print("[Yuumi] 解锁视角")
			end
		end
	end

	-- 自动扫描功能
	function slot_15_48.AutoScan()
		-- 添加错误处理
		if not slot_15_48.menu or not slot_15_48.menu.misc then
			return
		end

		if not slot_15_48.menu.misc.autoscan:get() then
			return
		end

		-- 限制扫描频率，每8秒检查一次
		if os.clock() - slot_15_48.last_scan_time < 8 then
			return
		end

		-- 检查player对象
		if not player then
			return
		end

		-- 检查是否有扫描饰品
		local scanSlot = nil
		for i = 0, 6 do
			local itemId = player:itemID(i)
			if itemId then
				-- 扫描饰品和真眼的ID
				if itemId == 3364 or itemId == 2055 then  -- 扫描饰品或控制守卫
					scanSlot = i + 6  -- 转换为spellSlot索引
					break
				end
			end
		end

		if scanSlot then
			local spellSlot = player:spellSlot(scanSlot)
			if spellSlot and spellSlot.cooldown <= 0 then
				-- 检查附近是否有敌方英雄或可疑区域
				local enemiesNearby = common.GetEnemyHeroesInRange(1200, player.pos)
				local shouldScan = false

				-- 如果附近有敌人但看不到，可能有隐身或眼位
				if #enemiesNearby > 0 then
					shouldScan = true
				end

				-- 检查重要区域（草丛等）
				if not shouldScan and navmesh.isGrass(player.pos) then
					shouldScan = true
				end

				if shouldScan then
					player:castSpell("pos", scanSlot, player.pos)
					slot_15_48.last_scan_time = os.clock()
					common.d_debug("Auto scan activated")
				end
			end
		end
	end

	-- 自动换扫描功能
	function slot_15_48.AutoSwapScan()
		-- 添加错误处理
		if not slot_15_48.menu or not slot_15_48.menu.misc then
			return
		end

		if not slot_15_48.menu.misc.autoswapscan:get() then
			return
		end

		local swapLevel = slot_15_48.menu.misc.swaplevel:get()

		-- 达到指定等级时自动换成扫描饰品
		if player.levelRef >= swapLevel then
			-- 检查当前饰品
			local trinketId = player:itemID(6)  -- 饰品栏是第7个位置，索引为6

			if trinketId then
				-- 如果当前不是扫描饰品，则购买扫描饰品
				if trinketId ~= 3364 and player.inShopRange then
					-- 使用shop API
					if trinketId ~= 0 then
						shop.sellItem(6)
					end
					shop.buyItem(3364, 6)  -- 购买扫描饰品到饰品栏
					common.d_debug("Auto swapped to scan trinket at level " .. player.levelRef)
				end
			end
		end
	end

	-- 娣诲姞鑷?鍔ㄨ喘涔拌?呭?囧姛鑳?
	function slot_15_48.AutoBuyItems()
		-- 只有当玩家在商店范围内且开启了自动购买选项时才执行
		if player.inShopRange and slot_15_48.menu.shop.autobuy:get() then
			-- 限制检查频率，每3秒检查一次
			if os.clock() - slot_15_48.last_shop_check < 3 then
				return
			end
			
			slot_15_48.last_shop_check = os.clock()
			
			local hasAnyItem = false
			local hasLunarstone = false -- 月石 (6617)
			local hasSpellthief = false -- 工资装 (3865)
			local hasRedemption = false -- 救赎 (3107)
			local hasCenser = false     -- 香炉 (3504)
			local hasMikaels = false    -- 米凯尔的祝福 (3222)
			local hasShurelya = false   -- 皇冠 (2065)
			
			-- 检查已有物品
			for i = 0, 5 do
				local itemId = player:itemID(i)
				
				-- 打印物品ID信息
				if slot_15_48.menu.shop.printitem:get() and itemId ~= 0 then
					local itemName = player:spellSlot(i + 6).name
					chat.print("物品栏 " .. i .. ": ID=" .. itemId .. ", 名称=" .. (itemName or "未知"))
				end
				
				if itemId ~= 0 then
					hasAnyItem = true
				end
				
				if itemId == 6617 then -- 月石
					hasLunarstone = true
				elseif itemId == 3865 then -- 工资装
					hasSpellthief = true
				elseif itemId == 3107 then -- 救赎
					hasRedemption = true
				elseif itemId == 3504 then -- 香炉
					hasCenser = true
				elseif itemId == 3222 then -- 米凯尔的祝福
					hasMikaels = true
				elseif itemId == 2065 then -- 皇冠
					hasShurelya = true
				end
			end
			
			-- 没有任何装备时买工资装
			if not hasAnyItem then
				player:buyItem(3865) -- 工资装
				common.d_debug("购买: 工资装")
				return -- 购买后直接返回等待下一次检查
			end
			
			-- 按新的优先级购买装备: 月石 > 香炉 > 米凯尔的祝福 > 其他
			
			-- 1. 月石 (最优先)
			if not hasLunarstone and player.gold >= 2100 then
				player:buyItem(6617) -- 月石
				common.d_debug("购买: 月石")
				return -- 购买后直接返回等待下一次检查
			end
			
			-- 2. 香炉 (第二优先)
			if not hasCenser and player.gold >= 3000 then
				player:buyItem(3504) -- 香炉
				common.d_debug("购买: 香炉")
				return -- 购买后直接返回等待下一次检查
			end
			
			-- 3. 米凯尔的祝福 (第三优先)
			if not hasMikaels and player.gold >= 3500 then
				player:buyItem(3222) -- 米凯尔的祝福
				common.d_debug("购买: 米凯尔的祝福")
				return -- 购买后直接返回等待下一次检查
			end
			
			-- 其余装备按原有顺序
			if not hasSpellthief and player.gold >= 500 then
				player:buyItem(3865) -- 工资装
				common.d_debug("购买: 工资装")
				return -- 购买后直接返回等待下一次检查
			end
			
			if not hasRedemption and player.gold >= 2300 then
				player:buyItem(3107) -- 救赎
				common.d_debug("购买: 救赎")
				return -- 购买后直接返回等待下一次检查
			end
			
			if not hasShurelya and player.gold >= 2300 then
				player:buyItem(2065) -- 皇冠
				common.d_debug("购买: 皇冠")
				return -- 购买后直接返回等待下一次检查
			end
		end
	end

return slot_15_48
