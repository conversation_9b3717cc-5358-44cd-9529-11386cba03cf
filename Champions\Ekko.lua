local ove_0_0 = "1.0"
local ove_0_1 = module.seek("evade")
local ove_0_2 = module.internal("pred")
local ove_0_3 = module.internal("TS")
local ove_0_4 = module.internal("orb")
local ove_0_5 = module.load("Brian", "Utility/common20")
local ove_0_6 = {
	speed = 1200,
	range = 825,
	delay = 0.25,
	boundingRadiusMod = 1,
	width = 135,
	collision = {
		minion = false,
		hero = false,
		wall = true
	}
}
local ove_0_7 = {
	delay = 0,
	range = 850,
	width = 45,
	boundingRadiusMod = 1,
	speed = math.huge,
	collision = {
		minion = false,
		hero = false,
		wall = false
	}
}
local ove_0_8 = {
	speed = 800,
	radius = 200,
	range = 800,
	delay = 0.25,
	boundingRadiusMod = 0
}
local ove_0_9 = {
	delay = 1,
	radius = 375,
	range = 1600,
	boundingRadiusMod = 0,
	speed = math.huge,
	collision = {
		minion = false,
		hero = false,
		wall = false
	}
}
local ove_0_10 = {
	delay = 0,
	radius = 75,
	range = 1600,
	boundingRadiusMod = 0,
	speed = math.huge,
	collision = {
		minion = false,
		hero = false,
		wall = false
	}
}
local ove_0_11 = {
	range = 750
}
local ove_0_12 = {
	range = 350
}
local ove_0_13 = {
	[0] = "Q",
	"W",
	"E",
	[-1] = "P",
	[3] = "R"
}
local ove_0_14 = menu("Brian" .. player.charName, "[Brian]艾克")

ove_0_14:menu("combo", "连招")
ove_0_14.combo:header("qss", "Q Settings")
ove_0_14.combo:boolean("qcombo", "Use Q", true)
ove_0_14.combo:boolean("qWaitCombo", "E 后延迟 Q", false)
ove_0_14.combo.qWaitCombo:set("tooltip", "此选项在 E 施法后延迟 Q 的施法，所以总是先施法 E 再施法 Q")
ove_0_14.combo:slider("Qdelayzz", "延迟时间（毫秒）", 100, 0, 1000, 50)
ove_0_14.combo:header("wss", "W 设置")
ove_0_14.combo:boolean("wcombo", "使用 W", false)
ove_0_14.combo:boolean("waarange", "仅在连招范围外使用 W", true)
ove_0_14.combo:header("ess", "E 设置")
ove_0_14.combo:boolean("ecombo", "使用 E", true)
ove_0_14.combo:dropdown("emode", "E 模式", 1, {
	"到目标",
	"到鼠标位置"
})
ove_0_14.combo:boolean("eaarange", "仅在普攻范围外使用 E", false)
ove_0_14:menu("rset", "R 设置")
--ove_0_14.rset:header("rss", "R Settings")
ove_0_14.rset:boolean("user", "使用 R", true)
ove_0_14.rset:dropdown("rmode", "R 模式", 1, {
	"自动 R",
	"连招 R"
})
ove_0_14.rset.rmode:set("tooltip", "自动 R 总是使用 R。连招 R 仅在按下空格键时使用 R")
ove_0_14.rset:boolean("killr", "对可击杀的敌人使用 R", true)
ove_0_14.rset:slider("rhp", "当艾克生命值百分比为： ", 20, 0, 100, 1)
ove_0_14.rset:boolean("wr", "如果可能，W 到 R 的位置", true)
ove_0_14:menu("harass", "骚扰")
--ove_0_14.harass:header("qss", "Harass Settings")
ove_0_14.harass:boolean("qharass", "Use Q", true)
ove_0_14.harass:boolean("eharass", "Use E", false)
ove_0_14:menu("junglec", "清野")
--ove_0_14.junglec:header("qss", "Jungle Clear Settings")
ove_0_14.junglec:boolean("qjungle", "Use Q", true)
ove_0_14.junglec:boolean("ejungle", "Use E", true)
ove_0_14:menu("misc", "杂项")
ove_0_14.misc:header("qss", "反突进")
ove_0_14.misc:boolean("EGap", "对突进敌人使用 Q", true)
ove_0_14.misc:menu("blacklist", "反突进列表")

local ove_0_15 = ove_0_5.GetEnemyHeroes()

for iter_0_0, iter_0_1 in ipairs(ove_0_15) do
	ove_0_14.misc.blacklist:boolean(iter_0_1.charName, "Block: " .. iter_0_1.charName, false)
end

--ove_0_14.misc:header("qss", "Other Settings")
ove_0_14.misc:boolean("ETurret", "对防御塔使用 E", true)
ove_0_14.misc:boolean("Qks", "用 Q 抢人头", true)
ove_0_14:menu("flee", "逃跑")
--ove_0_14.flee:header("qss", "Flee Settings")
ove_0_14.flee:keybind("fleekey", "逃跑键", "Z", nil)
ove_0_14.flee:boolean("fleeq", "在逃跑模式下使用 Q", true)
ove_0_14.flee:boolean("fleee", "在逃跑模式下使用 E", true)
ove_0_14.flee.fleee:set("tooltip", "在鼠标位置释放 E")
ove_0_14:menu("draws", "绘制")
ove_0_14.draws:header("qss", "主要绘制")
ove_0_14.draws:boolean("drawq", "Q 范围", true)
ove_0_14.draws:color("colorq", "颜色", 255, 153, 153, 255)
ove_0_14.draws:boolean("draww", "W范围", false)
ove_0_14.draws:color("colorw", "颜色", 255, 153, 153, 255)
ove_0_14.draws:boolean("drawe", "E范围", true)
ove_0_14.draws:color("colore", "颜色", 255, 153, 153, 255)
ove_0_14.draws:header("qssss", "附加绘制")
ove_0_14.draws:boolean("drawkill", "绘制可被 Q 击杀的小兵", true)
ove_0_14.draws:boolean("drawsoul", "绘制艾克 R 的幽灵周围", true)
ove_0_14.draws:boolean("drawsoulrange", "绘制 R 影响区域范围", true)
ove_0_14.draws:boolean("drawdamage", "显示伤害计算", true)
ove_0_14.draws:boolean("drawpassive", "显示被动层数", true)
ove_0_14:menu("keys", "按键")
--ove_0_14.keys:header("qss", "Keybind Settings")
ove_0_14.keys:keybind("combokey", "连招键", "Space", nil)
ove_0_14.keys:keybind("harasskey", "骚扰键", "C", nil)
ove_0_14.keys:keybind("clearkey", "清线键", "V", nil)
ove_0_14.keys:keybind("lastkey", "尾刀键", "X", nil)
--ove_0_14:header("headree", "~ 娛樂H合集 ~\x00\x00\x00\x00\x00")

local ove_0_16
local ove_0_17

local function ove_0_18(arg_1_0)
	if arg_1_0 and arg_1_0.owner == player and arg_1_0.name:find("Ekko") then
		ove_0_17 = arg_1_0
	end
end

local function ove_0_19(arg_2_0)
	if ove_0_17 and ove_0_17.ptr == arg_2_0.ptr then
		ove_0_17 = nil
	end
end

local function ove_0_20(arg_3_0)
	if arg_3_0 and arg_3_0.name and arg_3_0.name:find("R_TrailEnd") and not arg_3_0.name:find("R_TrailEnd_Red") then
		ove_0_16 = arg_3_0
	end
end

local function ove_0_21(arg_4_0)
	if ove_0_16 and ove_0_16.ptr == arg_4_0.ptr then
		ove_0_16 = nil
	end
end

local ove_0_22 = 0
local ove_0_23

local function ove_0_24(arg_5_0)
	if arg_5_0.name == "EkkoEAttack" and arg_5_0.owner == player then
		ove_0_4.core.set_pause_attack((2 / (0.625 + 0.625 * (player.attackSpeedMod - 1)) + network.latency) / 2 - 0.38)
	end

	if arg_5_0 and arg_5_0.owner == player and arg_5_0.name == "EkkoE" then
		ove_0_22 = game.time + ove_0_14.combo.Qdelayzz:get() / 1000
	end

	if arg_5_0 and (arg_5_0.name:find("BasicAttack") or arg_5_0.name:find("EkkoE")) and arg_5_0.owner == player then
		ove_0_23 = arg_5_0.target
	end
end

local function ove_0_25()
	if not player.isRecalling and player:spellSlot(0).state == 0 and ove_0_14.misc.EGap:get() then
		for iter_6_0 = 0, objManager.enemies_n - 1 do
			local slot_6_0 = objManager.enemies[iter_6_0]

			if slot_6_0.type == TYPE_HERO and slot_6_0.team == TEAM_ENEMY and slot_6_0 and ove_0_5.IsValidTarget(slot_6_0) and slot_6_0.path.isActive and slot_6_0.path.isDashing and player.pos:dist(slot_6_0.path.point[1]) < ove_0_6.range and ove_0_14.misc.blacklist[slot_6_0.charName] and not ove_0_14.misc.blacklist[slot_6_0.charName]:get() and player.pos2D:dist(slot_6_0.path.point2D[1]) < player.pos2D:dist(slot_6_0.path.point2D[0]) then
				local slot_6_1 = ove_0_2.linear.get_prediction(ove_0_6, slot_6_0)

				if slot_6_1 and slot_6_1.startPos:dist(slot_6_1.endPos) < ove_0_6.range and not ove_0_2.collision.get_prediction(ove_0_6, slot_6_1, slot_6_0) then
					player:castSpell("pos", 0, vec3(slot_6_1.endPos.x, slot_6_0.pos.y, slot_6_1.endPos.y))
				end
			end
		end
	end
end

local function ove_0_26(arg_7_0, arg_7_1)
	local slot_7_0 = {}

	for iter_7_0 = 0, objManager.enemies_n - 1 do
		local slot_7_1 = objManager.enemies[iter_7_0]

		if arg_7_1 >= arg_7_0:dist(slot_7_1.pos) and ove_0_5.IsValidTarget(slot_7_1) then
			slot_7_0[#slot_7_0 + 1] = slot_7_1
		end
	end

	return slot_7_0
end

local function ove_0_27(arg_8_0, arg_8_1)
	local slot_8_0 = {}

	for iter_8_0 = 0, objManager.allies_n - 1 do
		local slot_8_1 = objManager.allies[iter_8_0]

		if arg_8_1 > arg_8_0:dist(slot_8_1.pos) and ove_0_5.IsValidTarget(slot_8_1) then
			slot_8_0[#slot_8_0 + 1] = slot_8_1
		end
	end

	return slot_8_0
end

local function ove_0_28(arg_9_0, arg_9_1)
	local slot_9_0 = {}

	for iter_9_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
		local slot_9_1 = objManager.minions[TEAM_ENEMY][iter_9_0]

		if arg_9_1 > arg_9_0:dist(slot_9_1.pos) and ove_0_5.IsValidTarget(slot_9_1) then
			slot_9_0[#slot_9_0 + 1] = slot_9_1
		end
	end

	return slot_9_0
end

local function ove_0_29(arg_10_0, arg_10_1, arg_10_2)
	if arg_10_2 < ove_0_11.range then
		arg_10_0.obj = arg_10_1

		return true
	end
end

local function ove_0_30()
	return ove_0_3.get_result(ove_0_29).obj
end

local function ove_0_31(arg_12_0, arg_12_1, arg_12_2)
	if arg_12_2 < ove_0_6.range + 380 then
		arg_12_0.obj = arg_12_1

		return true
	end
end

local function ove_0_32()
	return ove_0_3.get_result(ove_0_31).obj
end

local function ove_0_33(arg_14_0, arg_14_1, arg_14_2)
	if arg_14_2 < ove_0_7.range then
		arg_14_0.obj = arg_14_1

		return true
	end
end

local function ove_0_34()
	return ove_0_3.get_result(ove_0_33).obj
end

local function ove_0_35(arg_16_0, arg_16_1, arg_16_2)
	if arg_16_2 < ove_0_9.range then
		arg_16_0.obj = arg_16_1

		return true
	end
end

local function ove_0_36()
	return ove_0_3.get_result(ove_0_35).obj
end

local function ove_0_37(arg_18_0, arg_18_1, arg_18_2)
	if arg_18_2 < ove_0_6.range then
		arg_18_0.obj = arg_18_1

		return true
	end
end

local function ove_0_38()
	return ove_0_3.get_result(ove_0_37).obj
end

local function ove_0_39(arg_20_0, arg_20_1, arg_20_2)
	if arg_20_2 < ove_0_11.range then
		arg_20_0.obj = arg_20_1

		return true
	end
end

local function ove_0_40()
	return ove_0_3.get_result(ove_0_39).obj
end

local ove_0_41 = false
local ove_0_42 = 0

local function ove_0_43()
	if ove_0_14.farming.togglefarm:get() then
		if ove_0_41 == false and os.clock() > ove_0_42 then
			ove_0_41 = true
			ove_0_42 = os.clock() + 0.3
		end

		if ove_0_41 == true and os.clock() > ove_0_42 then
			ove_0_41 = false
			ove_0_42 = os.clock() + 0.3
		end
	end
end

local ove_0_44 = {
	0.6,
	0.625,
	0.65,
	0.675,
	0.7
}
local ove_0_45 = {
	0.12,
	0.14,
	0.16,
	0.18,
	0.19
}
local ove_0_46 = {
	0.34,
	0.4,
	0.45,
	0.5,
	0.55
}
local ove_0_47 = {
	0.12,
	0.145,
	0.17,
	0.175,
	0.22
}
local ove_0_48 = {
	0.28,
	0.33,
	0.38
}
local ove_0_49 = {
	0.3,
	0.325,
	0.35,
	0.375,
	0.4
}
local ove_0_50 = {
	0.16,
	0.22,
	0.28,
	0.34,
	0.4
}
local ove_0_51 = {
	0.55,
	0.65,
	0.75
}
local ove_0_52 = {
	110,
	120,
	130,
	140,
	150,
	160,
	170,
	180,
	190,
	200,
	210,
	220,
	230,
	240,
	250,
	260,
	270,
	280,
	280,
	280,
	280,
	280,
	280,
	280,
	280,
	280,
	280,
	280,
	280,
	280
}
local ove_0_53 = {
	40,
	44.71,
	49.41,
	54.12,
	58.82,
	63.53,
	68.24,
	72.94,
	77.65,
	82.35,
	87.06,
	91.76,
	96.47,
	101.18,
	105.88,
	110.59,
	115.29,
	120,
	120,
	120,
	120,
	120,
	120,
	120,
	120,
	120,
	120,
	120,
	120,
	120
}
local ove_0_54 = {
	30,
	31.76,
	33.53,
	35.29,
	37.06,
	38.82,
	40.59,
	42.35,
	44.12,
	45.88,
	47.65,
	49.41,
	51.18,
	52.94,
	54.71,
	56.47,
	58.24,
	60,
	60,
	60,
	60,
	60,
	60,
	60,
	60,
	60,
	60,
	60,
	60,
	60
}
local ove_0_55 = {
	0.49,
	0.48,
	0.47,
	0.46,
	0.45,
	0.44,
	0.43,
	0.42,
	0.41,
	0.4,
	0.39,
	0.38,
	0.37,
	0.36,
	0.35,
	0.34,
	0.33,
	0.32,
	0.31
}
local ove_0_56 = {
	60,
	75,
	90,
	105,
	120
}

-- 艾克被动伤害计算函数
local ove_0_passive_base = {30, 40, 50, 60, 70, 80, 85, 90, 95, 100, 110, 120, 130, 140, 150, 160, 170, 180}

function GetPassiveDamage(target)
	local level = player.levelRef
	if level > 18 then level = 18 end
	
	local base_damage = ove_0_passive_base[level]
	local ap_ratio = 0.8 -- 80% AP加成
	local hp_ratio = 0.03 -- 目标最大生命值的3%
	
	local ap_damage = ove_0_5.GetTotalAP() * ap_ratio
	local hp_damage = target.maxHealth * hp_ratio
	
	local total_damage = base_damage + ap_damage + hp_damage
	
	-- 计算魔法抗性减伤
	local final_damage = ove_0_5.CalculateMagicDamage(target, total_damage, player)
	
	-- 应用减伤效果，与R伤害计算类似
	if target.buff.garenw then
		final_damage = final_damage * 0.4
	end
	
	if target.buff.meditate then
		final_damage = final_damage * (1 - ove_0_44[target:spellSlot(1).level])
	end
	
	if target.buff.gragaswself then
		final_damage = final_damage * (1 - ove_0_45[target:spellSlot(1).level])
	end
	
	if target.buff.warwicke then
		final_damage = final_damage * (1 - ove_0_46[target:spellSlot(2).level])
	end
	
	if target.buff.ireliawdefense then
		final_damage = final_damage * 0.5
	end
	
	if target.buff.galiow then
		final_damage = final_damage * (1 - ove_0_47[target:spellSlot(1).level])
	end
	
	if target.buff.galiorallybuff then
		final_damage = final_damage * (1 - ove_0_48[target:spellSlot(3).level])
	end
	
	if target.buff.braumeshieldbuff then
		final_damage = final_damage * (1 - ove_0_49[target:spellSlot(2).level])
	end
	
	if target.buff.annie then
		final_damage = final_damage * (1 - ove_0_50[target:spellSlot(2).level])
	end
	
	if target.buff.ferocioushowl then
		final_damage = final_damage * (1 - ove_0_51[target:spellSlot(3).level])
	end
	
	if target.buff.undyingrage or target.buff.kindredrnodeathbuff or target.buff[17] or target.buff[4] then
		final_damage = 0
	end
	
	if target.charName == "Blitzcrank" and target.buff.manabarriericon and not target.buff.manabarriercooldown then
		final_damage = final_damage - target.maxMana * 0.3
	end
	
	if player.buff.summonerexhaust then
		final_damage = final_damage * 0.6
	end
	
	-- 处理护盾和抵抗效果
	local target_level = target.levelRef
	if target_level > 18 then target_level = 18 end
	
	if target.buff["assets/perks/styles/resolve/boneplating/boneplating.lua"] then
		final_damage = final_damage - ove_0_54[target_level]
	end
	
	if target.buff["assets/perks/styles/sorcery/nullifyingorb/perknullifyingorbactive.lua"] then
		final_damage = final_damage - (ove_0_53[target_level] + ove_0_5.GetBonusAD(target) * 0.15 + ove_0_5.GetTotalAP(target) * 0.1)
	end
	
	if target.buff.fioraw then
		final_damage = 0
	end
	
	-- 检查是否有抵抗装备
	for i = 0, 5 do
		if target:itemID(i) == 3155 and not target.buff.hexdrinkertimercd then -- 玛莫提乌斯之噬
			final_damage = final_damage - ove_0_52[target_level]
		elseif target:itemID(i) == 3156 and not target.buff.hexdrinkertimercd then -- 饮魔刀
			final_damage = final_damage - 350
		end
	end
	
	return final_damage
end

function GetTotalComboDamage(target)
    local total = 0
    
    -- 计算Q伤害（出手+回程）
    if player:spellSlot(0).state == 0 then
        total = total + GetQDamageOut(target) + GetQDamageIn(target)
    end
    
    -- 计算E伤害（实际上是强化普攻）
    if player:spellSlot(2).state == 0 then
        local e_damage = ove_0_5.CalculateMagicDamage(target, 50 + 40 * player:spellSlot(2).level + ove_0_5.GetTotalAP() * 0.4, player)
        -- 使用player.attackDamage或者如果有common库中的函数来获取AD
        local attack_damage = ove_0_5.GetTotalAD and ove_0_5.GetTotalAD() or (player.baseDamage or 0)
        total = total + e_damage + attack_damage
    end
    
    -- 计算R伤害
    if player:spellSlot(3).state == 0 and ove_0_16 and ove_0_16.pos:dist(target.pos) <= ove_0_12.range then
        total = total + GetRDamage(target)
    end
    
    -- 计算被动伤害（假设能触发）
    if target.buff and target.buff["ekkopassivestacks"] and target.buff["ekkopassivestacks"].stacks == 2 then
        total = total + GetPassiveDamage(target)
    end
    
    return total
end

function GetRDamage(arg_23_0)
	local slot_23_0 = player.levelRef

	if player.levelRef > 18 then
		local slot_23_1 = 18
	end

	local slot_23_2 = arg_23_0.levelRef

	if arg_23_0.levelRef > 18 then
		slot_23_2 = 18
	end

	local slot_23_3 = 0

	if player:spellSlot(3).level > 0 then
		slot_23_3 = ove_0_5.CalculateMagicDamage(arg_23_0, ove_0_56[player:spellSlot(3).level] + ove_0_5.GetTotalAP() * 1.5, player)
	end

	if arg_23_0.buff.garenw then
		slot_23_3 = slot_23_3 * 0.4
	end

	if arg_23_0.buff.meditate then
		slot_23_3 = slot_23_3 * (1 - ove_0_44[arg_23_0:spellSlot(1).level])
	end

	if arg_23_0.buff.gragaswself then
		slot_23_3 = slot_23_3 * (1 - ove_0_45[arg_23_0:spellSlot(1).level])
	end

	if arg_23_0.buff.warwicke then
		slot_23_3 = slot_23_3 * (1 - ove_0_46[arg_23_0:spellSlot(2).level])
	end

	if arg_23_0.buff.ireliawdefense then
		slot_23_3 = slot_23_3 * 0.5
	end

	if arg_23_0.buff.galiow then
		slot_23_3 = slot_23_3 * (1 - ove_0_47[arg_23_0:spellSlot(1).level])
	end

	if arg_23_0.buff.galiorallybuff then
		slot_23_3 = slot_23_3 * (1 - ove_0_48[arg_23_0:spellSlot(3).level])
	end

	if arg_23_0.buff.braumeshieldbuff then
		slot_23_3 = slot_23_3 * (1 - ove_0_49[arg_23_0:spellSlot(2).level])
	end

	if arg_23_0.buff.annie then
		slot_23_3 = slot_23_3 * (1 - ove_0_50[arg_23_0:spellSlot(2).level])
	end

	if arg_23_0.buff.ferocioushowl then
		slot_23_3 = slot_23_3 * (1 - ove_0_51[arg_23_0:spellSlot(3).level])
	end

	if arg_23_0.buff.undyingrage then
		slot_23_3 = 0
	end

	if arg_23_0.buff.kindredrnodeathbuff then
		slot_23_3 = 0
	end

	if arg_23_0.buff[17] or arg_23_0.buff[4] then
		slot_23_3 = 0
	end

	if arg_23_0.charName == "Blitzcrank" and arg_23_0.buff.manabarriericon and not arg_23_0.buff.manabarriercooldown then
		slot_23_3 = slot_23_3 - arg_23_0.maxMana * 0.3
	end

	if player.buff.summonerexhaust then
		slot_23_3 = slot_23_3 * 0.6
	end

	if arg_23_0.buff["assets/perks/styles/resolve/boneplating/boneplating.lua"] then
		slot_23_3 = slot_23_3 - ove_0_54[slot_23_2]
	end

	if arg_23_0.buff["assets/perks/styles/sorcery/nullifyingorb/perknullifyingorbactive.lua"] then
		slot_23_3 = slot_23_3 - (ove_0_53[slot_23_2] + ove_0_5.GetBonusAD(arg_23_0) * 0.15 + ove_0_5.GetTotalAP(arg_23_0) * 0.1)
	end

	if arg_23_0.buff.fioraw then
		slot_23_3 = 0
	end

	for iter_23_0 = 0, 5 do
		if arg_23_0:itemID(iter_23_0) == 3155 then
			if not arg_23_0.buff.hexdrinkertimercd then
				slot_23_3 = slot_23_3 - ove_0_52[slot_23_2]
			end

			if arg_23_0.buff.hexdrinkertimercd then
				-- block empty
			end
		end

		if arg_23_0:itemID(iter_23_0) == 3156 then
			if not arg_23_0.buff.hexdrinkertimercd then
				slot_23_3 = slot_23_3 - 350
			end

			if arg_23_0.buff.hexdrinkertimercd then
				-- block empty
			end
		end
	end

	return slot_23_3
end

local ove_0_57 = {
	60,
	75,
	90,
	105,
	120
}

function GetQDamageOut(arg_24_0)
	local slot_24_0 = player.levelRef

	if player.levelRef > 18 then
		local slot_24_1 = 18
	end

	local slot_24_2 = arg_24_0.levelRef

	if arg_24_0.levelRef > 18 then
		slot_24_2 = 18
	end

	local slot_24_3 = 0

	if player:spellSlot(0).level > 0 then
		slot_24_3 = ove_0_5.CalculateMagicDamage(arg_24_0, ove_0_57[player:spellSlot(0).level] + ove_0_5.GetTotalAP() * 0.3, player)
	end

	if arg_24_0.buff.garenw then
		slot_24_3 = slot_24_3 * 0.4
	end

	if arg_24_0.buff.meditate then
		slot_24_3 = slot_24_3 * (1 - ove_0_44[arg_24_0:spellSlot(1).level])
	end

	if arg_24_0.buff.gragaswself then
		slot_24_3 = slot_24_3 * (1 - ove_0_45[arg_24_0:spellSlot(1).level])
	end

	if arg_24_0.buff.warwicke then
		slot_24_3 = slot_24_3 * (1 - ove_0_46[arg_24_0:spellSlot(2).level])
	end

	if arg_24_0.buff.ireliawdefense then
		slot_24_3 = slot_24_3 * 0.5
	end

	if arg_24_0.buff.galiow then
		slot_24_3 = slot_24_3 * (1 - ove_0_47[arg_24_0:spellSlot(1).level])
	end

	if arg_24_0.buff.galiorallybuff then
		slot_24_3 = slot_24_3 * (1 - ove_0_48[arg_24_0:spellSlot(3).level])
	end

	if arg_24_0.buff.braumeshieldbuff then
		slot_24_3 = slot_24_3 * (1 - ove_0_49[arg_24_0:spellSlot(2).level])
	end

	if arg_24_0.buff.annie then
		slot_24_3 = slot_24_3 * (1 - ove_0_50[arg_24_0:spellSlot(2).level])
	end

	if arg_24_0.buff.ferocioushowl then
		slot_24_3 = slot_24_3 * (1 - ove_0_51[arg_24_0:spellSlot(3).level])
	end

	if arg_24_0.buff.undyingrage then
		slot_24_3 = 0
	end

	if arg_24_0.buff.kindredrnodeathbuff then
		slot_24_3 = 0
	end

	if arg_24_0.buff[17] or arg_24_0.buff[4] then
		slot_24_3 = 0
	end

	if arg_24_0.charName == "Blitzcrank" and arg_24_0.buff.manabarriericon and not arg_24_0.buff.manabarriercooldown then
		slot_24_3 = slot_24_3 - arg_24_0.maxMana * 0.3
	end

	if player.buff.summonerexhaust then
		slot_24_3 = slot_24_3 * 0.6
	end

	if arg_24_0.buff["assets/perks/styles/resolve/boneplating/boneplating.lua"] then
		slot_24_3 = slot_24_3 - ove_0_54[slot_24_2]
	end

	if arg_24_0.buff["assets/perks/styles/sorcery/nullifyingorb/perknullifyingorbactive.lua"] then
		slot_24_3 = slot_24_3 - (ove_0_53[slot_24_2] + ove_0_5.GetBonusAD(arg_24_0) * 0.15 + ove_0_5.GetTotalAP(arg_24_0) * 0.1)
	end

	if arg_24_0.buff.fioraw then
		slot_24_3 = 0
	end

	if arg_24_0.buff.exaltedwithbaronnashorminion then
		if math.floor(game.time / 60) <= 20 then
			if arg_24_0.charName:find("MinionRanged") then
				slot_24_3 = slot_24_3 * 0.5
			end

			if arg_24_0.charName:find("MinionMelee") then
				slot_24_3 = slot_24_3 * 0.5
			end
		end

		if math.floor(game.time / 60) >= 21 and not (math.floor(game.time / 60) >= 40) then
			if arg_24_0.charName:find("MinionRanged") then
				slot_24_3 = slot_24_3 * ove_0_55[math.floor(game.time / 60) - 20]
			end

			if arg_24_0.charName:find("MinionMelee") then
				slot_24_3 = slot_24_3 * ove_0_55[math.floor(game.time / 60) - 20]
			end
		end

		if math.floor(game.time / 60) >= 40 then
			if arg_24_0.charName:find("MinionRanged") then
				slot_24_3 = slot_24_3 * 0.3
			end

			if arg_24_0.charName:find("MinionMelee") then
				slot_24_3 = slot_24_3 * 0.3
			end
		end
	end

	return slot_24_3
end

local ove_0_58 = {
	40,
	65,
	90,
	115,
	140
}

function GetQDamageIn(arg_25_0)
	local slot_25_0 = player.levelRef

	if player.levelRef > 18 then
		local slot_25_1 = 18
	end

	local slot_25_2 = arg_25_0.levelRef

	if arg_25_0.levelRef > 18 then
		slot_25_2 = 18
	end

	local slot_25_3 = 0

	if player:spellSlot(0).level > 0 then
		slot_25_3 = ove_0_5.CalculateMagicDamage(arg_25_0, ove_0_58[player:spellSlot(0).level] + ove_0_5.GetTotalAP() * 0.6, player)
	end

	if arg_25_0.buff.garenw then
		slot_25_3 = slot_25_3 * 0.4
	end

	if arg_25_0.buff.meditate then
		slot_25_3 = slot_25_3 * (1 - ove_0_44[arg_25_0:spellSlot(1).level])
	end

	if arg_25_0.buff.gragaswself then
		slot_25_3 = slot_25_3 * (1 - ove_0_45[arg_25_0:spellSlot(1).level])
	end

	if arg_25_0.buff.warwicke then
		slot_25_3 = slot_25_3 * (1 - ove_0_46[arg_25_0:spellSlot(2).level])
	end

	if arg_25_0.buff.ireliawdefense then
		slot_25_3 = slot_25_3 * 0.5
	end

	if arg_25_0.buff.galiow then
		slot_25_3 = slot_25_3 * (1 - ove_0_47[arg_25_0:spellSlot(1).level])
	end

	if arg_25_0.buff.galiorallybuff then
		slot_25_3 = slot_25_3 * (1 - ove_0_48[arg_25_0:spellSlot(3).level])
	end

	if arg_25_0.buff.braumeshieldbuff then
		slot_25_3 = slot_25_3 * (1 - ove_0_49[arg_25_0:spellSlot(2).level])
	end

	if arg_25_0.buff.annie then
		slot_25_3 = slot_25_3 * (1 - ove_0_50[arg_25_0:spellSlot(2).level])
	end

	if arg_25_0.buff.ferocioushowl then
		slot_25_3 = slot_25_3 * (1 - ove_0_51[arg_25_0:spellSlot(3).level])
	end

	if arg_25_0.buff.undyingrage then
		slot_25_3 = 0
	end

	if arg_25_0.buff.kindredrnodeathbuff then
		slot_25_3 = 0
	end

	if arg_25_0.buff[17] or arg_25_0.buff[4] then
		slot_25_3 = 0
	end

	if arg_25_0.charName == "Blitzcrank" and arg_25_0.buff.manabarriericon and not arg_25_0.buff.manabarriercooldown then
		slot_25_3 = slot_25_3 - arg_25_0.maxMana * 0.3
	end

	if player.buff.summonerexhaust then
		slot_25_3 = slot_25_3 * 0.6
	end

	if arg_25_0.buff["assets/perks/styles/resolve/boneplating/boneplating.lua"] then
		slot_25_3 = slot_25_3 - ove_0_54[slot_25_2]
	end

	if arg_25_0.buff["assets/perks/styles/sorcery/nullifyingorb/perknullifyingorbactive.lua"] then
		slot_25_3 = slot_25_3 - (ove_0_53[slot_25_2] + ove_0_5.GetBonusAD(arg_25_0) * 0.15 + ove_0_5.GetTotalAP(arg_25_0) * 0.1)
	end

	if arg_25_0.buff.fioraw then
		slot_25_3 = 0
	end

	if arg_25_0.buff.exaltedwithbaronnashorminion then
		if math.floor(game.time / 60) <= 20 then
			if arg_25_0.charName:find("MinionRanged") then
				slot_25_3 = slot_25_3 * 0.5
			end

			if arg_25_0.charName:find("MinionMelee") then
				slot_25_3 = slot_25_3 * 0.5
			end
		end

		if math.floor(game.time / 60) >= 21 and not (math.floor(game.time / 60) >= 40) then
			if arg_25_0.charName:find("MinionRanged") then
				slot_25_3 = slot_25_3 * ove_0_55[math.floor(game.time / 60) - 20]
			end

			if arg_25_0.charName:find("MinionMelee") then
				slot_25_3 = slot_25_3 * ove_0_55[math.floor(game.time / 60) - 20]
			end
		end

		if math.floor(game.time / 60) >= 40 then
			if arg_25_0.charName:find("MinionRanged") then
				slot_25_3 = slot_25_3 * 0.3
			end

			if arg_25_0.charName:find("MinionMelee") then
				slot_25_3 = slot_25_3 * 0.3
			end
		end
	end

	return slot_25_3
end

local function ove_0_59(arg_26_0, arg_26_1, arg_26_2)
	if ove_0_2.trace.circular.hardlock(arg_26_0, arg_26_1, arg_26_2) then
		return true
	end

	if ove_0_2.trace.circular.hardlockmove(arg_26_0, arg_26_1, arg_26_2) then
		return true
	end

	if arg_26_2 and ove_0_5.IsValidTarget(arg_26_2) and (player.pos:dist(arg_26_2) <= player.attackRange + player.boundingRadius + arg_26_2.boundingRadius or arg_26_1.startPos:dist(arg_26_1.endPos) <= 625) then
		return true
	end

	if ove_0_2.trace.newpath(arg_26_2, 0.033, 0.5) then
		return true
	end
end

ove_0_4.combat.register_f_after_attack(function()
	if ove_0_14.keys.clearkey:get() and ove_0_14.misc.ETurret:get() and ove_0_23 and ove_0_23.type == TYPE_TURRET and player.pos:dist(ove_0_23.pos) <= ove_0_7.range and player:spellSlot(2).state == 0 then
		player:castSpell("pos", 2, mousePos)
		player:attack(ove_0_23)

		return "on_after_attack_hydra"
	end

	if ove_0_14.keys.clearkey:get() and ove_0_14.misc.ETurret:get() and ove_0_23 and ove_0_23.type == TYPE_INHIB and player.pos:dist(ove_0_23.pos) <= ove_0_7.range and player:spellSlot(2).state == 0 then
		player:castSpell("pos", 2, mousePos)
		player:attack(ove_0_23)

		return "on_after_attack_hydra"
	end

	if ove_0_14.keys.clearkey:get() and ove_0_14.misc.ETurret:get() and ove_0_23 and ove_0_23.type == TYPE_NEXUS and player.pos:dist(ove_0_23.pos) <= ove_0_7.range and player:spellSlot(2).state == 0 then
		player:castSpell("pos", 2, mousePos)
		player:attack(ove_0_23)

		return "on_after_attack_hydra"
	end
end)

local function ove_0_60()
	if ove_0_14.combo.ecombo:get() and ove_0_14.combo.emode:get() == 1 and player:spellSlot(2).state == 0 then
		if ove_0_14.combo.eaarange:get() then
			local slot_28_0 = ove_0_34()

			if ove_0_5.IsValidTarget(slot_28_0) and slot_28_0.pos:dist(player.pos) <= ove_0_7.range then
				if slot_28_0.pos:dist(player.pos) > 350 then
					local slot_28_1 = ove_0_2.linear.get_prediction(ove_0_7, slot_28_0)

					if slot_28_1 and slot_28_1.startPos:dist(slot_28_1.endPos) < ove_0_7.range then
						player:castSpell("pos", 2, vec3(slot_28_1.endPos.x, slot_28_0.pos.y, slot_28_1.endPos.y))
					end
				end

				if slot_28_0.pos:dist(player.pos) <= 350 then
					-- block empty
				end
			end
		end

		if not ove_0_14.combo.eaarange:get() then
			local slot_28_2 = ove_0_34()

			if ove_0_5.IsValidTarget(slot_28_2) and slot_28_2.pos:dist(player.pos) <= ove_0_7.range then
				local slot_28_3 = ove_0_2.linear.get_prediction(ove_0_7, slot_28_2)

				if slot_28_3 and slot_28_3.startPos:dist(slot_28_3.endPos) < ove_0_7.range then
					player:castSpell("pos", 2, vec3(slot_28_3.endPos.x, slot_28_2.pos.y, slot_28_3.endPos.y))
				end
			end
		end
	end

	if ove_0_14.combo.ecombo:get() and ove_0_14.combo.emode:get() == 2 and player:spellSlot(2).state == 0 then
		if ove_0_14.combo.eaarange:get() then
			local slot_28_4 = ove_0_34()

			if ove_0_5.IsValidTarget(slot_28_4) and slot_28_4.pos:dist(player.pos) <= ove_0_7.range then
				if slot_28_4.pos:dist(player.pos) > 350 then
					local slot_28_5 = ove_0_2.linear.get_prediction(ove_0_7, slot_28_4)

					if slot_28_5 and slot_28_5.startPos:dist(slot_28_5.endPos) < ove_0_7.range then
						player:castSpell("pos", 2, mousePos)
					end
				end

				if slot_28_4.pos:dist(player.pos) <= 350 then
					-- block empty
				end
			end
		end

		if not ove_0_14.combo.eaarange:get() then
			local slot_28_6 = ove_0_34()

			if ove_0_5.IsValidTarget(slot_28_6) and slot_28_6.pos:dist(player.pos) <= ove_0_7.range then
				local slot_28_7 = ove_0_2.linear.get_prediction(ove_0_7, slot_28_6)

				if slot_28_7 and slot_28_7.startPos:dist(slot_28_7.endPos) < ove_0_7.range then
					player:castSpell("pos", 2, mousePos)
				end
			end
		end
	end

	if ove_0_14.combo.qcombo:get() then
		if not ove_0_14.combo.qWaitCombo:get() then
			if player:spellSlot(0).state == 0 then
				local slot_28_8 = ove_0_38()

				if ove_0_5.IsValidTarget(slot_28_8) and slot_28_8.pos:dist(player.pos) <= ove_0_6.range then
					local slot_28_9 = ove_0_2.linear.get_prediction(ove_0_6, slot_28_8)

					if slot_28_9 and slot_28_9.startPos:dist(slot_28_9.endPos) < ove_0_6.range and not ove_0_2.collision.get_prediction(ove_0_6, slot_28_9, slot_28_8) then
						player:castSpell("pos", 0, vec3(slot_28_9.endPos.x, slot_28_8.pos.y, slot_28_9.endPos.y))
					end
				end
			end
		else
			if not ove_0_14.combo.ecombo:get() and player:spellSlot(0).state == 0 then
				local slot_28_10 = ove_0_38()

				if ove_0_5.IsValidTarget(slot_28_10) and slot_28_10.pos:dist(player.pos) <= ove_0_6.range then
					local slot_28_11 = ove_0_2.linear.get_prediction(ove_0_6, slot_28_10)

					if slot_28_11 and slot_28_11.startPos:dist(slot_28_11.endPos) < ove_0_6.range and not ove_0_2.collision.get_prediction(ove_0_6, slot_28_11, slot_28_10) then
						player:castSpell("pos", 0, vec3(slot_28_11.endPos.x, slot_28_10.pos.y, slot_28_11.endPos.y))
					end
				end
			end

			if player:spellSlot(0).state == 0 and player.buff.ekkoeattackbuff and ove_0_22 < game.time then
				local slot_28_12 = ove_0_38()

				if ove_0_5.IsValidTarget(slot_28_12) and slot_28_12.pos:dist(player.pos) <= ove_0_6.range then
					local slot_28_13 = ove_0_2.linear.get_prediction(ove_0_6, slot_28_12)

					if slot_28_13 and slot_28_13.startPos:dist(slot_28_13.endPos) < ove_0_6.range and not ove_0_2.collision.get_prediction(ove_0_6, slot_28_13, slot_28_12) then
						player:castSpell("pos", 0, vec3(slot_28_13.endPos.x, slot_28_12.pos.y, slot_28_13.endPos.y))
					end
				end
			end

			if player:spellSlot(0).state == 0 and player:spellSlot(2).state ~= 8 and player:spellSlot(2).state ~= 0 and ove_0_22 < game.time then
				local slot_28_14 = ove_0_38()

				if ove_0_5.IsValidTarget(slot_28_14) and slot_28_14.pos:dist(player.pos) <= ove_0_6.range then
					local slot_28_15 = ove_0_2.linear.get_prediction(ove_0_6, slot_28_14)

					if slot_28_15 and slot_28_15.startPos:dist(slot_28_15.endPos) < ove_0_6.range and not ove_0_2.collision.get_prediction(ove_0_6, slot_28_15, slot_28_14) then
						player:castSpell("pos", 0, vec3(slot_28_15.endPos.x, slot_28_14.pos.y, slot_28_15.endPos.y))
					end
				end
			end
		end
	end

	if ove_0_14.combo.wcombo:get() and player:spellSlot(1).state == 0 then
		local slot_28_16 = ove_0_36()

		if slot_28_16 and slot_28_16.isVisible and ove_0_5.IsValidTarget(slot_28_16) and slot_28_16.pos:dist(player.pos) < ove_0_9.range then
			if ove_0_14.combo.waarange:get() then
				if slot_28_16.pos:dist(player.pos) > 800 then
					local slot_28_17 = ove_0_2.circular.get_prediction(ove_0_9, slot_28_16)

					if slot_28_17 and slot_28_17.startPos:dist(slot_28_17.endPos) < ove_0_9.range then
						player:castSpell("pos", 1, vec3(slot_28_17.endPos.x, slot_28_16.pos.y, slot_28_17.endPos.y))
					end
				end

				if slot_28_16.pos:dist(player.pos) < 800 then
					-- block empty
				end
			end

			if not ove_0_14.combo.waarange:get() then
				local slot_28_18 = ove_0_2.circular.get_prediction(ove_0_9, slot_28_16)

				if slot_28_18 and slot_28_18.startPos:dist(slot_28_18.endPos) < ove_0_9.range then
					player:castSpell("pos", 1, vec3(slot_28_18.endPos.x, slot_28_16.pos.y, slot_28_18.endPos.y))
				end
			end
		end
	end

	if ove_0_14.rset.user:get() and ove_0_14.rset.rmode:get() == 2 and player:spellSlot(3).state == 0 and #ove_0_5.GetEnemyHeroesInRange(600) >= 1 and player.health / player.maxHealth * 100 <= ove_0_14.rset.rhp:get() then
		if ove_0_14.rset.wr:get() then
			if player:spellSlot(1).state == 0 and ove_0_17 then
				local slot_28_19 = ove_0_2.circular.get_prediction(ove_0_10, ove_0_17)

				if slot_28_19 and slot_28_19.startPos:dist(slot_28_19.endPos) < ove_0_10.range and ove_0_17.pos:dist(player.pos) < ove_0_10.range then
					player:castSpell("pos", 1, vec3(slot_28_19.endPos.x, ove_0_17.pos.y, slot_28_19.endPos.y))
					player:castSpell("self", 3)
				end
			end

			if player:spellSlot(1).state ~= 0 then
				player:castSpell("self", 3)
			end
		end

		if not ove_0_14.rset.wr:get() then
			player:castSpell("self", 3)
		end
	end
end

local function ove_0_61()
	if ove_0_14.harass.qharass:get() and player:spellSlot(0).state == 0 then
		local slot_29_0 = ove_0_38()

		if ove_0_5.IsValidTarget(slot_29_0) and slot_29_0.pos:dist(player.pos) <= ove_0_6.range then
			local slot_29_1 = ove_0_2.linear.get_prediction(ove_0_6, slot_29_0)

			if slot_29_1 and slot_29_1.startPos:dist(slot_29_1.endPos) < ove_0_6.range and not ove_0_2.collision.get_prediction(ove_0_6, slot_29_1, slot_29_0) then
				player:castSpell("pos", 0, vec3(slot_29_1.endPos.x, slot_29_0.pos.y, slot_29_1.endPos.y))
			end
		end
	end

	if ove_0_14.harass.eharass:get() and ove_0_14.combo.emode:get() == 1 and player:spellSlot(2).state == 0 then
		if ove_0_14.combo.eaarange:get() then
			local slot_29_2 = ove_0_34()

			if ove_0_5.IsValidTarget(slot_29_2) and slot_29_2.pos:dist(player.pos) <= ove_0_7.range then
				if slot_29_2.pos:dist(player.pos) > 350 then
					local slot_29_3 = ove_0_2.linear.get_prediction(ove_0_7, slot_29_2)

					if slot_29_3 and slot_29_3.startPos:dist(slot_29_3.endPos) < ove_0_7.range then
						player:castSpell("pos", 2, vec3(slot_29_3.endPos.x, slot_29_2.pos.y, slot_29_3.endPos.y))
					end
				end

				if slot_29_2.pos:dist(player.pos) <= 350 then
					-- block empty
				end
			end
		end

		if not ove_0_14.combo.eaarange:get() then
			local slot_29_4 = ove_0_34()

			if ove_0_5.IsValidTarget(slot_29_4) and slot_29_4.pos:dist(player.pos) <= ove_0_7.range then
				local slot_29_5 = ove_0_2.linear.get_prediction(ove_0_7, slot_29_4)

				if slot_29_5 and slot_29_5.startPos:dist(slot_29_5.endPos) < ove_0_7.range then
					player:castSpell("pos", 2, vec3(slot_29_5.endPos.x, slot_29_4.pos.y, slot_29_5.endPos.y))
				end
			end
		end
	end

	if ove_0_14.harass.eharass:get() and ove_0_14.combo.emode:get() == 2 and player:spellSlot(2).state == 0 then
		if ove_0_14.combo.eaarange:get() then
			local slot_29_6 = ove_0_34()

			if ove_0_5.IsValidTarget(slot_29_6) and slot_29_6.pos:dist(player.pos) <= ove_0_7.range then
				if slot_29_6.pos:dist(player.pos) > 350 then
					local slot_29_7 = ove_0_2.linear.get_prediction(ove_0_7, slot_29_6)

					if slot_29_7 and slot_29_7.startPos:dist(slot_29_7.endPos) < ove_0_7.range then
						player:castSpell("pos", 2, mousePos)
					end
				end

				if slot_29_6.pos:dist(player.pos) <= 350 then
					-- block empty
				end
			end
		end

		if not ove_0_14.combo.eaarange:get() then
			local slot_29_8 = ove_0_34()

			if ove_0_5.IsValidTarget(slot_29_8) and slot_29_8.pos:dist(player.pos) <= ove_0_7.range then
				local slot_29_9 = ove_0_2.linear.get_prediction(ove_0_7, slot_29_8)

				if slot_29_9 and slot_29_9.startPos:dist(slot_29_9.endPos) < ove_0_7.range then
					player:castSpell("pos", 2, mousePos)
				end
			end
		end
	end
end

local function ove_0_62()
	local slot_30_0 = ove_0_5.GetEnemyHeroes()

	for iter_30_0, iter_30_1 in ipairs(slot_30_0) do
		if iter_30_1 and ove_0_5.IsValidTarget(iter_30_1) and not ove_0_5.CheckBuffType(iter_30_1, 17) then
			local slot_30_1 = ove_0_5.GetShieldedHealth("ALL", iter_30_1) - iter_30_1.physicalShield

			if ove_0_14.misc.Qks:get() and player:spellSlot(0).state == 0 and vec3(iter_30_1.x, iter_30_1.y, iter_30_1.z):dist(player) < ove_0_6.range and slot_30_1 <= GetQDamageOut(iter_30_1) then
				local slot_30_2 = ove_0_2.linear.get_prediction(ove_0_6, iter_30_1)

				if slot_30_2 and slot_30_2.startPos:dist(slot_30_2.endPos) < ove_0_6.range and not ove_0_2.collision.get_prediction(ove_0_6, slot_30_2, iter_30_1) then
					player:castSpell("pos", 0, vec3(slot_30_2.endPos.x, mousePos.y, slot_30_2.endPos.y))
				end
			end
		end
	end
end

local function ove_0_63()
	if ove_0_14.rset.killr:get() and player:spellSlot(3).state == 0 then
		local slot_31_0 = ove_0_5.GetEnemyHeroes()

		for iter_31_0, iter_31_1 in ipairs(slot_31_0) do
			if iter_31_1 and ove_0_5.IsValidTarget(iter_31_1) and not ove_0_5.CheckBuffType(iter_31_1, 17) then
				local slot_31_1 = ove_0_5.GetShieldedHealth("ALL", iter_31_1) - iter_31_1.physicalShield

				if ove_0_17 and #ove_0_26(ove_0_17.pos, 250) >= 1 and slot_31_1 <= GetRDamage(iter_31_1) then
					player:castSpell("self", 3)
				end
			end
		end
	end
end

local function ove_0_64()
	if ove_0_14.flee.fleekey:get() then
		player:move(vec3(mousePos.x, mousePos.y, mousePos.z), ove_0_4.menu.movement.limit_packets:get())
	end

	if ove_0_14.flee.fleekey:get() and ove_0_14.flee.fleeq:get() and player:spellSlot(0).state == 0 then
		local slot_32_0 = ove_0_38()

		if ove_0_5.IsValidTarget(slot_32_0) and slot_32_0.pos:dist(player.pos) <= ove_0_6.range then
			local slot_32_1 = ove_0_2.linear.get_prediction(ove_0_6, slot_32_0)

			if slot_32_1 and slot_32_1.startPos:dist(slot_32_1.endPos) < ove_0_6.range then
				player:castSpell("pos", 0, vec3(slot_32_1.endPos.x, slot_32_0.pos.y, slot_32_1.endPos.y))
			end
		end
	end

	if ove_0_14.flee.fleekey:get() and ove_0_14.flee.fleee:get() and player:spellSlot(2).state == 0 then
		player:castSpell("pos", 2, mousePos)
	end
end

local function ove_0_65()
	if ove_0_14.junglec.qjungle:get() and player:spellSlot(0).state == 0 then
		for iter_33_0 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local slot_33_0 = objManager.minions[TEAM_NEUTRAL][iter_33_0]

			if slot_33_0 and slot_33_0.isVisible and slot_33_0.moveSpeed > 0 and slot_33_0.isTargetable and not slot_33_0.isDead and slot_33_0.pos:dist(player.pos) < ove_0_6.range then
				local slot_33_1 = vec3(slot_33_0.x, slot_33_0.y, slot_33_0.z)

				if slot_33_1:dist(player.pos) <= ove_0_6.range then
					local slot_33_2 = ove_0_2.linear.get_prediction(ove_0_6, slot_33_0)

					if slot_33_2 and slot_33_2.startPos:dist(slot_33_2.endPos) < ove_0_6.range then
						player:castSpell("pos", 0, vec3(slot_33_2.endPos.x, slot_33_1.y, slot_33_2.endPos.y))
					end
				end
			end
		end
	end

	if ove_0_14.junglec.ejungle:get() and player:spellSlot(2).state == 0 then
		for iter_33_1 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
			local slot_33_3 = objManager.minions[TEAM_NEUTRAL][iter_33_1]

			if slot_33_3 and slot_33_3.isVisible and slot_33_3.moveSpeed > 0 and slot_33_3.isTargetable and not slot_33_3.isDead and slot_33_3.pos:dist(player.pos) < ove_0_7.range then
				local slot_33_4 = vec3(slot_33_3.x, slot_33_3.y, slot_33_3.z)

				if slot_33_4:dist(player.pos) <= ove_0_7.range then
					local slot_33_5 = ove_0_2.linear.get_prediction(ove_0_7, slot_33_3)

					if slot_33_5 and slot_33_5.startPos:dist(slot_33_5.endPos) < ove_0_7.range then
						player:castSpell("pos", 2, vec3(slot_33_5.endPos.x, slot_33_4.y, slot_33_5.endPos.y))
					end
				end
			end
		end
	end
end

local function ove_0_66()
	if ove_0_14.combo.qWaitCombo:get() then
		ove_0_14.combo.Qdelayzz:set("visible", true)
	else
		ove_0_14.combo.Qdelayzz:set("visible", false)
	end
end

local function ove_0_67()
	if ove_0_14.rset.user:get() and ove_0_14.rset.rmode:get() == 1 and player:spellSlot(3).state == 0 and #ove_0_5.GetEnemyHeroesInRange(600) >= 1 and player.health / player.maxHealth * 100 <= ove_0_14.rset.rhp:get() then
		if ove_0_14.rset.wr:get() then
			if player:spellSlot(1).state == 0 and ove_0_17 then
				local slot_35_0 = ove_0_2.circular.get_prediction(ove_0_10, ove_0_17)

				if slot_35_0 and slot_35_0.startPos:dist(slot_35_0.endPos) < ove_0_10.range and ove_0_17.pos:dist(player.pos) < ove_0_10.range then
					player:castSpell("pos", 1, vec3(slot_35_0.endPos.x, ove_0_17.pos.y, slot_35_0.endPos.y))
					player:castSpell("self", 3)
				end
			end

			if player:spellSlot(1).state ~= 0 then
				player:castSpell("self", 3)
			end
		end

		if not ove_0_14.rset.wr:get() then
			player:castSpell("self", 3)
		end
	end

	ove_0_62()
	ove_0_25()
	ove_0_63()
	ove_0_64()

	if ove_0_14.keys.combokey:get() then
		ove_0_60()
	end

	if ove_0_14.keys.clearkey:get() then
		ove_0_65()
	end

	if ove_0_14.keys.harasskey:get() then
		ove_0_61()
	end

	ove_0_66()
end

local function ove_0_68()
	if player.isOnScreen then
		if ove_0_14.draws.drawq:get() then
			graphics.draw_circle(player.pos, ove_0_6.range, 2, ove_0_14.draws.colorq:get(), 50)
		end

		if ove_0_14.draws.draww:get() then
			graphics.draw_circle(player.pos, ove_0_9.range, 2, ove_0_14.draws.colorw:get(), 50)
		end

		if ove_0_14.draws.drawe:get() then
			graphics.draw_circle(player.pos, ove_0_8.range, 2, ove_0_14.draws.colore:get(), 50)
		end

		if ove_0_14.draws.drawsoul:get() and ove_0_17 then
			graphics.draw_circle(ove_0_17, 100, 2, graphics.argb(255, 150, 255, 150), 50)
		end

		if ove_0_14.draws.drawsoulrange:get() and ove_0_16 then
			graphics.draw_circle(ove_0_16.pos, ove_0_12.range, 2, graphics.argb(255, 150, 255, 255), 50)
		end
		
		-- 显示敌方英雄的伤害信息
		if ove_0_14.draws.drawdamage:get() then
			for i = 0, objManager.enemies_n - 1 do
				local enemy = objManager.enemies[i]
				if enemy and enemy.isVisible and not enemy.isDead and ove_0_5.IsValidTarget(enemy) then
					-- 计算总伤害
					local total_damage = GetTotalComboDamage(enemy)
					local hp_percent = math.floor((total_damage / enemy.health) * 100)
					
					-- 获取屏幕坐标
					local pos = graphics.world_to_screen(enemy.pos)
					if pos then
						-- 显示伤害信息
						local text = string.format("伤害: %.0f (%.0f%%)", total_damage, hp_percent)
						local color = graphics.argb(255, 255, 255, 255)
						
						-- 根据伤害比例改变颜色
						if hp_percent >= 100 then
							color = graphics.argb(255, 0, 255, 0)  -- 绿色，可击杀
						elseif hp_percent >= 75 then
							color = graphics.argb(255, 255, 255, 0)  -- 黄色，高伤害
						end
						
						graphics.draw_text_2D(text, 15, pos.x - 50, pos.y - 35, color)
						
						-- 显示被动层数
						if ove_0_14.draws.drawpassive:get() and enemy.buff and enemy.buff["ekkopassivestacks"] then
							local stacks = enemy.buff["ekkopassivestacks"].stacks
							local passive_text = string.format("被动: %d/3", stacks)
							graphics.draw_text_2D(passive_text, 15, pos.x - 50, pos.y - 55, graphics.argb(255, 0, 255, 255))
						end
					end
				end
			end
		end

		if ove_0_14.draws.drawkill:get() and player:spellSlot(0).state == 0 then
			for iter_36_0 = 0, objManager.minions.size[TEAM_ENEMY] - 1 do
				local slot_36_0 = objManager.minions[TEAM_ENEMY][iter_36_0]

				if slot_36_0 and slot_36_0.isVisible and slot_36_0.moveSpeed > 0 and slot_36_0.isTargetable and not slot_36_0.isDead and slot_36_0.type == TYPE_MINION and slot_36_0.pos:dist(player.pos) < ove_0_6.range + 300 then
					local slot_36_1 = vec3(slot_36_0.x, slot_36_0.y, slot_36_0.z)

					if GetQDamageOut(slot_36_0) + GetQDamageIn(slot_36_0) >= slot_36_0.health then
						graphics.draw_circle(slot_36_1, 100, 2, graphics.argb(155, 255, 255, 200), 50)
					end
				end
			end

			for iter_36_1 = 0, objManager.minions.size[TEAM_NEUTRAL] - 1 do
				local slot_36_2 = objManager.minions[TEAM_NEUTRAL][iter_36_1]

				if slot_36_2 and slot_36_2.isVisible and slot_36_2.moveSpeed > 0 and slot_36_2.isTargetable and not slot_36_2.isDead and slot_36_2.type == TYPE_MINION and slot_36_2.pos:dist(player.pos) < ove_0_6.range + 300 then
					local slot_36_3 = vec3(slot_36_2.x, slot_36_2.y, slot_36_2.z)

					if GetQDamageOut(slot_36_2) + GetQDamageIn(slot_36_2) >= slot_36_2.health then
						graphics.draw_circle(slot_36_3, 100, 2, graphics.argb(155, 255, 255, 200), 50)
					end
				end
			end
		end
	end
end

cb.add(cb.create_minion, ove_0_18)
cb.add(cb.delete_minion, ove_0_19)
cb.add(cb.create_particle, ove_0_20)
cb.add(cb.delete_particle, ove_0_21)
ove_0_3.load_to_menu(ove_0_14)
ove_0_4.combat.register_f_pre_tick(ove_0_67)
cb.add(cb.spell, ove_0_24)
cb.add(cb.draw, ove_0_68)
